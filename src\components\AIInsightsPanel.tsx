
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Brain, 
  Newspaper, 
  MessageSquare, 
  TrendingUp, 
  TrendingDown,
  BarChart3,
  Zap,
  Target
} from 'lucide-react';
import { useAISignals } from '@/hooks/useAISignals';

const AIInsightsPanel = () => {
  const { aiSignals, newsData, socialData, isGeneratingAI } = useAISignals();

  const getSentimentColor = (sentiment: string) => {
    switch (sentiment) {
      case 'POSITIVE':
        return 'text-green-400 bg-green-900/20 border-green-500';
      case 'NEGATIVE':
        return 'text-red-400 bg-red-900/20 border-red-500';
      default:
        return 'text-yellow-400 bg-yellow-900/20 border-yellow-500';
    }
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'HIGH':
        return 'text-red-400';
      case 'MEDIUM':
        return 'text-yellow-400';
      default:
        return 'text-green-400';
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-IN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const recentNews = newsData.slice(0, 5);
  const recentSocial = socialData.slice(0, 5);
  const aiSignalsCount = aiSignals.length;

  return (
    <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Brain className="w-5 h-5 text-cyan-400" />
            AI Market Intelligence
            {isGeneratingAI && (
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-cyan-400 rounded-full animate-pulse"></div>
                <span className="text-xs text-cyan-400">Processing...</span>
              </div>
            )}
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="bg-cyan-900/20 text-cyan-400 border-cyan-500">
              {aiSignalsCount} AI Signals
            </Badge>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="signals" className="space-y-4">
          <TabsList className="grid w-full grid-cols-4 bg-slate-700/50">
            <TabsTrigger value="signals">AI Signals</TabsTrigger>
            <TabsTrigger value="news">News</TabsTrigger>
            <TabsTrigger value="social">Social</TabsTrigger>
            <TabsTrigger value="insights">Insights</TabsTrigger>
          </TabsList>

          <TabsContent value="signals" className="space-y-3">
            {aiSignals.length === 0 ? (
              <div className="text-center py-8 text-slate-400">
                <Brain className="w-8 h-8 mx-auto mb-2 opacity-50" />
                <p>No AI signals generated yet</p>
                <p className="text-xs">AI analysis in progress...</p>
              </div>
            ) : (
              <div className="space-y-2 max-h-80 overflow-y-auto">
                {aiSignals.slice(0, 10).map((signal) => (
                  <div
                    key={signal.id}
                    className="p-3 bg-slate-700/30 rounded-lg border border-slate-600"
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <span className="font-medium text-white">{signal.symbol}</span>
                        <Badge 
                          className={`${signal.signal === 'BUY' ? 'bg-green-900/20 text-green-400 border-green-500' : 
                                    signal.signal === 'SELL' ? 'bg-red-900/20 text-red-400 border-red-500' : 
                                    'bg-yellow-900/20 text-yellow-400 border-yellow-500'}`}
                        >
                          {signal.signal === 'BUY' ? <TrendingUp className="w-3 h-3 mr-1" /> : 
                           signal.signal === 'SELL' ? <TrendingDown className="w-3 h-3 mr-1" /> : 
                           <Target className="w-3 h-3 mr-1" />}
                          {signal.signal}
                        </Badge>
                      </div>
                      <div className="text-sm font-medium text-cyan-400">
                        {signal.confidence.toFixed(0)}%
                      </div>
                    </div>
                    <p className="text-xs text-slate-400 mb-2 line-clamp-2">
                      {signal.reasoning}
                    </p>
                    <div className="flex items-center justify-between text-xs text-slate-500">
                      <span>AI Enhanced</span>
                      <span>{formatTime(signal.timestamp)}</span>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="news" className="space-y-3">
            <div className="space-y-2 max-h-80 overflow-y-auto">
              {recentNews.map((article) => (
                <div
                  key={article.id}
                  className="p-3 bg-slate-700/30 rounded-lg border border-slate-600"
                >
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex-1">
                      <h4 className="text-sm font-medium text-white mb-1 line-clamp-2">
                        {article.title}
                      </h4>
                      <div className="flex items-center gap-2">
                        <Newspaper className="w-3 h-3 text-slate-400" />
                        <span className="text-xs text-slate-400">{article.source}</span>
                        <Badge 
                          variant="outline" 
                          className={`text-xs ${getSentimentColor(article.sentiment.label)}`}
                        >
                          {article.sentiment.label}
                        </Badge>
                      </div>
                    </div>
                    <div className={`text-xs font-medium ${getImpactColor(article.impact)}`}>
                      {article.impact}
                    </div>
                  </div>
                  <div className="flex items-center justify-between text-xs text-slate-500">
                    <span>{article.symbols.join(', ')}</span>
                    <span>{formatTime(article.timestamp)}</span>
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="social" className="space-y-3">
            <div className="space-y-2 max-h-80 overflow-y-auto">
              {recentSocial.map((post) => (
                <div
                  key={post.id}
                  className="p-3 bg-slate-700/30 rounded-lg border border-slate-600"
                >
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex-1">
                      <p className="text-sm text-white mb-1 line-clamp-2">
                        {post.content}
                      </p>
                      <div className="flex items-center gap-2">
                        <MessageSquare className="w-3 h-3 text-slate-400" />
                        <span className="text-xs text-slate-400">{post.platform}</span>
                        <Badge 
                          variant="outline" 
                          className={`text-xs ${getSentimentColor(post.sentiment.label)}`}
                        >
                          {post.sentiment.label}
                        </Badge>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center justify-between text-xs text-slate-500">
                    <span>{post.engagement.likes} likes, {post.engagement.shares} shares</span>
                    <span>{formatTime(post.timestamp)}</span>
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="insights" className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-3">
                <h4 className="text-sm font-medium text-slate-300">Market Sentiment</h4>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-slate-400">News Sentiment:</span>
                    <span className="text-green-400">Positive</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-slate-400">Social Sentiment:</span>
                    <span className="text-yellow-400">Neutral</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-slate-400">Overall Score:</span>
                    <span className="text-cyan-400">+0.25</span>
                  </div>
                </div>
              </div>
              
              <div className="space-y-3">
                <h4 className="text-sm font-medium text-slate-300">AI Metrics</h4>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-slate-400">Accuracy:</span>
                    <span className="text-green-400">78%</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-slate-400">Confidence:</span>
                    <span className="text-cyan-400">85%</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-slate-400">Signals Today:</span>
                    <span className="text-white">{aiSignalsCount}</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="text-sm font-medium text-slate-300">Key Insights</h4>
              <div className="space-y-1 text-sm text-slate-400">
                <p>• Strong bullish sentiment detected in NIFTY50 sector</p>
                <p>• High social media activity around banking stocks</p>
                <p>• ML models showing 72% accuracy in 4h predictions</p>
                <p>• News sentiment correlation with price movements: 0.68</p>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default AIInsightsPanel;

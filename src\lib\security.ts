
import { z } from 'zod';

// Input sanitization functions
export const sanitizeString = (input: string): string => {
  return input
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim();
};

export const sanitizeEmail = (email: string): string => {
  return email.toLowerCase().trim().replace(/[^\w@.-]/g, '');
};

export const sanitizeNumericInput = (input: string): string => {
  return input.replace(/[^0-9.-]/g, '');
};

// Validation schemas
export const authLoginSchema = z.object({
  email: z.string().email('Invalid email format').min(1, 'Email is required'),
  password: z.string().min(8, 'Password must be at least 8 characters').max(100, 'Password too long')
});

export const authSignupSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters').max(50, 'Name too long'),
  email: z.string().email('Invalid email format').min(1, 'Email is required'),
  phone: z.string().regex(/^\+?[\d\s-()]+$/, 'Invalid phone number format').min(10, 'Phone number too short'),
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain uppercase, lowercase and number'),
  confirmPassword: z.string()
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"]
});

export const apiKeySchema = z.object({
  key: z.string().min(10, 'API key too short').max(200, 'API key too long'),
  service: z.enum(['ALPHA_VANTAGE', 'FINNHUB', 'IEX_CLOUD'])
});

export const paymentCardSchema = z.object({
  number: z.string().regex(/^\d{4}\s\d{4}\s\d{4}\s\d{4}$/, 'Invalid card number format'),
  expiry: z.string().regex(/^\d{2}\/\d{2}$/, 'Invalid expiry format (MM/YY)'),
  cvv: z.string().regex(/^\d{3,4}$/, 'Invalid CVV'),
  name: z.string().min(2, 'Name too short').max(50, 'Name too long')
});

// Crypto utilities for secure storage
export const generateSecureKey = (): string => {
  const array = new Uint8Array(32);
  crypto.getRandomValues(array);
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
};

// Session management
export const generateSessionToken = (): string => {
  return crypto.randomUUID();
};

export const validateSession = (token: string): boolean => {
  const sessionData = localStorage.getItem(`session_${token}`);
  if (!sessionData) return false;
  
  try {
    const session = JSON.parse(sessionData);
    return session.expires > Date.now();
  } catch {
    return false;
  }
};

// Rate limiting
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

export const checkRateLimit = (key: string, maxRequests: number = 10, windowMs: number = 60000): boolean => {
  const now = Date.now();
  const current = rateLimitMap.get(key);
  
  if (!current || now > current.resetTime) {
    rateLimitMap.set(key, { count: 1, resetTime: now + windowMs });
    return true;
  }
  
  if (current.count >= maxRequests) {
    return false;
  }
  
  current.count++;
  return true;
};

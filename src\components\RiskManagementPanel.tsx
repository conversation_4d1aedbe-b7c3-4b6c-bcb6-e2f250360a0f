
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Shield, AlertTriangle, CheckCircle, TrendingDown } from 'lucide-react';
import { useRiskManagement } from '@/hooks/useRiskManagement';

const RiskManagementPanel = () => {
  const { getRiskMetrics, riskParams, account } = useRiskManagement();
  const riskMetrics = getRiskMetrics();

  if (!riskMetrics) return null;

  const getRiskColor = (value: number, threshold: number) => {
    if (value >= threshold * 0.9) return 'text-red-400';
    if (value >= threshold * 0.7) return 'text-yellow-400';
    return 'text-green-400';
  };

  const getRiskBadgeColor = (value: number, threshold: number) => {
    if (value >= threshold * 0.9) return 'bg-red-900/20 text-red-400 border-red-500';
    if (value >= threshold * 0.7) return 'bg-yellow-900/20 text-yellow-400 border-yellow-500';
    return 'bg-green-900/20 text-green-400 border-green-500';
  };

  return (
    <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="w-5 h-5 text-orange-400" />
          Risk Management Dashboard
          {riskMetrics.tradingHalted && (
            <Badge className="bg-red-900/20 text-red-400 border-red-500">
              <AlertTriangle className="w-3 h-3 mr-1" />
              Trading Halted
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Risk Metrics Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm text-slate-400">Portfolio Risk</span>
              <Badge className={getRiskBadgeColor(riskMetrics.portfolioRisk, riskParams.maxPortfolioRisk)}>
                {riskMetrics.portfolioRisk.toFixed(1)}%
              </Badge>
            </div>
            <div className="w-full bg-slate-700 rounded-full h-2">
              <div 
                className={`h-2 rounded-full ${
                  riskMetrics.portfolioRisk >= riskParams.maxPortfolioRisk * 0.9 ? 'bg-red-500' :
                  riskMetrics.portfolioRisk >= riskParams.maxPortfolioRisk * 0.7 ? 'bg-yellow-500' : 'bg-green-500'
                }`}
                style={{width: `${Math.min(100, (riskMetrics.portfolioRisk / riskParams.maxPortfolioRisk) * 100)}%`}}
              />
            </div>
            <span className="text-xs text-slate-500">Limit: {riskParams.maxPortfolioRisk}%</span>
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm text-slate-400">Daily P&L</span>
              <Badge className={account.dailyPnL >= 0 ? 'bg-green-900/20 text-green-400 border-green-500' : 'bg-red-900/20 text-red-400 border-red-500'}>
                ₹{account.dailyPnL.toLocaleString()}
              </Badge>
            </div>
            <div className="w-full bg-slate-700 rounded-full h-2">
              <div 
                className={`h-2 rounded-full ${
                  riskMetrics.dailyLossRemaining <= riskParams.dailyLossLimit * 0.2 ? 'bg-red-500' :
                  riskMetrics.dailyLossRemaining <= riskParams.dailyLossLimit * 0.5 ? 'bg-yellow-500' : 'bg-green-500'
                }`}
                style={{width: `${Math.max(0, (riskMetrics.dailyLossRemaining / riskParams.dailyLossLimit) * 100)}%`}}
              />
            </div>
            <span className="text-xs text-slate-500">Remaining: ₹{riskMetrics.dailyLossRemaining.toLocaleString()}</span>
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm text-slate-400">Drawdown</span>
              <Badge className={getRiskBadgeColor(Math.abs(riskMetrics.drawdown), riskParams.maxDrawdown)}>
                {riskMetrics.drawdown.toFixed(1)}%
              </Badge>
            </div>
            <div className="w-full bg-slate-700 rounded-full h-2">
              <div 
                className={`h-2 rounded-full ${
                  Math.abs(riskMetrics.drawdown) >= riskParams.maxDrawdown * 0.9 ? 'bg-red-500' :
                  Math.abs(riskMetrics.drawdown) >= riskParams.maxDrawdown * 0.7 ? 'bg-yellow-500' : 'bg-green-500'
                }`}
                style={{width: `${Math.min(100, (Math.abs(riskMetrics.drawdown) / riskParams.maxDrawdown) * 100)}%`}}
              />
            </div>
            <span className="text-xs text-slate-500">Limit: {riskParams.maxDrawdown}%</span>
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm text-slate-400">Margin Used</span>
              <Badge className={getRiskBadgeColor(riskMetrics.marginUsed, 80)}>
                {riskMetrics.marginUsed.toFixed(1)}%
              </Badge>
            </div>
            <div className="w-full bg-slate-700 rounded-full h-2">
              <div 
                className={`h-2 rounded-full ${
                  riskMetrics.marginUsed >= 90 ? 'bg-red-500' :
                  riskMetrics.marginUsed >= 70 ? 'bg-yellow-500' : 'bg-blue-500'
                }`}
                style={{width: `${Math.min(100, riskMetrics.marginUsed)}%`}}
              />
            </div>
            <span className="text-xs text-slate-500">Available: ₹{account.availableBalance.toLocaleString()}</span>
          </div>
        </div>

        {/* Risk Parameters */}
        <div className="border-t border-slate-700 pt-4">
          <h4 className="text-sm font-medium text-slate-300 mb-3">Active Risk Parameters</h4>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-3 text-sm">
            <div className="flex justify-between">
              <span className="text-slate-400">Max Risk/Trade:</span>
              <span className="text-white">{riskParams.maxRiskPerTrade}%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-slate-400">Min Risk:Reward:</span>
              <span className="text-white">1:{riskParams.minRiskRewardRatio}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-slate-400">Max Single Stock:</span>
              <span className="text-white">{riskParams.maxSingleStockExposure}%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-slate-400">Max Sector:</span>
              <span className="text-white">{riskParams.maxSectorExposure}%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-slate-400">Daily Loss Limit:</span>
              <span className="text-white">₹{riskParams.dailyLossLimit.toLocaleString()}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-slate-400">Max Drawdown:</span>
              <span className="text-white">{riskParams.maxDrawdown}%</span>
            </div>
          </div>
        </div>

        {/* Status Indicators */}
        <div className="border-t border-slate-700 pt-4">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              {riskMetrics.tradingHalted ? (
                <AlertTriangle className="w-4 h-4 text-red-400" />
              ) : (
                <CheckCircle className="w-4 h-4 text-green-400" />
              )}
              <span className={`text-sm ${riskMetrics.tradingHalted ? 'text-red-400' : 'text-green-400'}`}>
                {riskMetrics.tradingHalted ? 'Trading Halted' : 'Trading Active'}
              </span>
            </div>
            
            <div className="flex items-center gap-2">
              <TrendingDown className="w-4 h-4 text-blue-400" />
              <span className="text-sm text-slate-300">
                Risk Level: {
                  riskMetrics.portfolioRisk < 5 ? 'Low' :
                  riskMetrics.portfolioRisk < 8 ? 'Medium' : 'High'
                }
              </span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default RiskManagementPanel;

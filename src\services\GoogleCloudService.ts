import { SentimentScore } from '@/types/ai';

// Google Cloud API endpoints (free tier quotas)
const GOOGLE_CLOUD_ENDPOINTS = {
  NATURAL_LANGUAGE: 'https://language.googleapis.com/v1/documents:analyzeSentiment',
  TRANSLATE: 'https://translation.googleapis.com/language/translate/v2',
  TEXT_TO_SPEECH: 'https://texttospeech.googleapis.com/v1/text:synthesize',
  SPEECH_TO_TEXT: 'https://speech.googleapis.com/v1/speech:recognize',
  VISION: 'https://vision.googleapis.com/v1/images:annotate'
};

// Free quota limits per month
const FREE_QUOTAS = {
  NATURAL_LANGUAGE: 5000, // 5,000 units/month
  TRANSLATE: 500000, // 500,000 characters/month
  TEXT_TO_SPEECH: 4000000, // 4M characters/month
  SPEECH_TO_TEXT: 60, // 60 minutes/month
  VISION: 1000 // 1,000 units/month
};

export class GoogleCloudService {
  private static apiKey: string | null = null;
  private static usageTracker = new Map<string, number>();

  /**
   * Set the Google Cloud API key
   */
  static setApiKey(key: string): void {
    this.apiKey = key;
  }

  /**
   * Get API key from environment or stored location
   */
  private static getApiKey(): string {
    if (this.apiKey) return this.apiKey;
    
    // Try to get from localStorage for demo purposes
    const storedKey = localStorage.getItem('google_cloud_api_key');
    if (storedKey) {
      this.apiKey = storedKey;
      return storedKey;
    }
    
    throw new Error('Google Cloud API key not configured');
  }

  /**
   * Check if within free quota limits
   */
  private static checkQuota(service: keyof typeof FREE_QUOTAS, units: number): boolean {
    const currentUsage = this.usageTracker.get(service) || 0;
    const limit = FREE_QUOTAS[service];
    
    if (currentUsage + units > limit) {
      console.warn(`Google Cloud ${service} quota exceeded. Current: ${currentUsage}, Limit: ${limit}`);
      return false;
    }
    
    return true;
  }

  /**
   * Track API usage
   */
  private static trackUsage(service: keyof typeof FREE_QUOTAS, units: number): void {
    const currentUsage = this.usageTracker.get(service) || 0;
    this.usageTracker.set(service, currentUsage + units);
  }

  /**
   * Enhanced sentiment analysis using Google Cloud Natural Language API
   */
  static async analyzeSentiment(text: string): Promise<SentimentScore> {
    try {
      if (!this.checkQuota('NATURAL_LANGUAGE', 1)) {
        throw new Error('Natural Language API quota exceeded');
      }

      const apiKey = this.getApiKey();
      const response = await fetch(`${GOOGLE_CLOUD_ENDPOINTS.NATURAL_LANGUAGE}?key=${apiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          document: {
            type: 'PLAIN_TEXT',
            content: text
          },
          encodingType: 'UTF8'
        })
      });

      if (!response.ok) {
        throw new Error(`Google Cloud API error: ${response.statusText}`);
      }

      const data = await response.json();
      this.trackUsage('NATURAL_LANGUAGE', 1);

      // Convert Google Cloud sentiment to our format
      const sentiment = data.documentSentiment;
      const polarity = sentiment.score; // Range: -1.0 to 1.0
      const magnitude = sentiment.magnitude; // Range: 0.0 to +inf
      
      // Calculate confidence based on magnitude (higher magnitude = more confident)
      const confidence = Math.min(1.0, magnitude / 2.0);
      
      let label: 'POSITIVE' | 'NEGATIVE' | 'NEUTRAL' = 'NEUTRAL';
      if (polarity > 0.25) label = 'POSITIVE';
      else if (polarity < -0.25) label = 'NEGATIVE';

      return {
        polarity,
        confidence,
        label,
        subjectivity: magnitude // Use magnitude as subjectivity indicator
      };

    } catch (error) {
      console.error('Google Cloud sentiment analysis failed:', error);
      // Fallback to basic sentiment analysis
      return this.fallbackSentimentAnalysis(text);
    }
  }

  /**
   * Translate text using Google Translate API
   */
  static async translateText(text: string, targetLanguage: string = 'en'): Promise<string> {
    try {
      const textLength = text.length;
      if (!this.checkQuota('TRANSLATE', textLength)) {
        throw new Error('Translation API quota exceeded');
      }

      const apiKey = this.getApiKey();
      const response = await fetch(`${GOOGLE_CLOUD_ENDPOINTS.TRANSLATE}?key=${apiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          q: text,
          target: targetLanguage,
          format: 'text'
        })
      });

      if (!response.ok) {
        throw new Error(`Google Translate API error: ${response.statusText}`);
      }

      const data = await response.json();
      this.trackUsage('TRANSLATE', textLength);

      return data.data.translations[0].translatedText;

    } catch (error) {
      console.error('Google Translate failed:', error);
      return text; // Return original text if translation fails
    }
  }

  /**
   * Convert text to speech using Google Cloud Text-to-Speech
   */
  static async textToSpeech(text: string, languageCode: string = 'en-US'): Promise<string> {
    try {
      const textLength = text.length;
      if (!this.checkQuota('TEXT_TO_SPEECH', textLength)) {
        throw new Error('Text-to-Speech API quota exceeded');
      }

      const apiKey = this.getApiKey();
      const response = await fetch(`${GOOGLE_CLOUD_ENDPOINTS.TEXT_TO_SPEECH}?key=${apiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          input: { text },
          voice: {
            languageCode,
            name: `${languageCode}-Standard-A`
          },
          audioConfig: {
            audioEncoding: 'MP3'
          }
        })
      });

      if (!response.ok) {
        throw new Error(`Text-to-Speech API error: ${response.statusText}`);
      }

      const data = await response.json();
      this.trackUsage('TEXT_TO_SPEECH', textLength);

      return data.audioContent; // Base64 encoded audio

    } catch (error) {
      console.error('Google Text-to-Speech failed:', error);
      throw error;
    }
  }

  /**
   * Recognize speech using Google Cloud Speech-to-Text
   */
  static async speechToText(audioData: string): Promise<string> {
    try {
      if (!this.checkQuota('SPEECH_TO_TEXT', 1)) {
        throw new Error('Speech-to-Text API quota exceeded');
      }

      const apiKey = this.getApiKey();
      const response = await fetch(`${GOOGLE_CLOUD_ENDPOINTS.SPEECH_TO_TEXT}?key=${apiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          config: {
            encoding: 'WEBM_OPUS',
            sampleRateHertz: 48000,
            languageCode: 'en-US'
          },
          audio: {
            content: audioData
          }
        })
      });

      if (!response.ok) {
        throw new Error(`Speech-to-Text API error: ${response.statusText}`);
      }

      const data = await response.json();
      this.trackUsage('SPEECH_TO_TEXT', 1);

      if (data.results && data.results.length > 0) {
        return data.results[0].alternatives[0].transcript;
      }

      return '';

    } catch (error) {
      console.error('Google Speech-to-Text failed:', error);
      throw error;
    }
  }

  /**
   * Analyze images using Google Cloud Vision API
   */
  static async analyzeImage(imageBase64: string): Promise<any> {
    try {
      if (!this.checkQuota('VISION', 1)) {
        throw new Error('Vision API quota exceeded');
      }

      const apiKey = this.getApiKey();
      const response = await fetch(`${GOOGLE_CLOUD_ENDPOINTS.VISION}?key=${apiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          requests: [{
            image: {
              content: imageBase64
            },
            features: [
              { type: 'TEXT_DETECTION', maxResults: 10 },
              { type: 'OBJECT_LOCALIZATION', maxResults: 10 },
              { type: 'LABEL_DETECTION', maxResults: 10 }
            ]
          }]
        })
      });

      if (!response.ok) {
        throw new Error(`Vision API error: ${response.statusText}`);
      }

      const data = await response.json();
      this.trackUsage('VISION', 1);

      return data.responses[0];

    } catch (error) {
      console.error('Google Vision API failed:', error);
      throw error;
    }
  }

  /**
   * Fallback sentiment analysis
   */
  private static fallbackSentimentAnalysis(text: string): SentimentScore {
    const positiveWords = ['good', 'great', 'excellent', 'positive', 'bullish', 'up', 'gain', 'profit'];
    const negativeWords = ['bad', 'terrible', 'negative', 'bearish', 'down', 'loss', 'crash'];
    
    const words = text.toLowerCase().split(/\W+/);
    let positiveCount = 0;
    let negativeCount = 0;
    
    words.forEach(word => {
      if (positiveWords.includes(word)) positiveCount++;
      if (negativeWords.includes(word)) negativeCount++;
    });
    
    const total = positiveCount + negativeCount;
    const polarity = total > 0 ? (positiveCount - negativeCount) / total : 0;
    const confidence = Math.min(1, total / 10);
    
    let label: 'POSITIVE' | 'NEGATIVE' | 'NEUTRAL' = 'NEUTRAL';
    if (polarity > 0.2) label = 'POSITIVE';
    else if (polarity < -0.2) label = 'NEGATIVE';
    
    return { polarity, confidence, label, subjectivity: confidence };
  }

  /**
   * Get current usage statistics
   */
  static getUsageStats(): Record<string, { used: number; limit: number; remaining: number }> {
    const stats: Record<string, { used: number; limit: number; remaining: number }> = {};
    
    Object.keys(FREE_QUOTAS).forEach(service => {
      const used = this.usageTracker.get(service) || 0;
      const limit = FREE_QUOTAS[service as keyof typeof FREE_QUOTAS];
      stats[service] = {
        used,
        limit,
        remaining: Math.max(0, limit - used)
      };
    });
    
    return stats;
  }

  /**
   * Reset usage tracking (call monthly)
   */
  static resetUsageTracking(): void {
    this.usageTracker.clear();
  }
}
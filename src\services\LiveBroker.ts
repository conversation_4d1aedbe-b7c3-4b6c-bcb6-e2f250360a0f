import { BrokerInterface } from './BrokerInterface';
import { Order, Trade, BrokerAccount, BrokerPosition, OrderRequest, BrokerResponse } from '@/types/broker';
import { marketDataService } from './MarketDataService';

export class LiveBroker extends BrokerInterface {
  private orderCounter = 1;
  private tradeCounter = 1;
  private marketDataUpdateInterval: NodeJS.Timeout | null = null;

  protected initializeAccount(): BrokerAccount {
    return {
      accountId: 'live_account_001',
      balance: 100000, // Starting with 1 lakh for demo
      equity: 100000,
      availableBalance: 100000,
      usedMargin: 0,
      freeMargin: 100000,
      marginLevel: 0,
      unrealizedPnL: 0,
      dailyPnL: 0,
      positions: [],
      orders: [],
      trades: []
    };
  }

  async connect(): Promise<BrokerResponse<boolean>> {
    console.log('Connecting to Live Market Data...');
    
    // Test market data connection
    const testConnection = await marketDataService.testConnection();
    if (!testConnection) {
      return { 
        success: false, 
        error: 'Failed to connect to market data sources. Please check your internet connection.' 
      };
    }

    this.connected = true;
    
    // Start real-time market data updates
    this.startMarketDataUpdates();
    
    console.log('Available data sources:', marketDataService.getAvailableSources());
    
    return { 
      success: true, 
      data: true, 
      message: `Connected to live market data via: ${marketDataService.getAvailableSources().join(', ')}` 
    };
  }

  async disconnect(): Promise<BrokerResponse<boolean>> {
    console.log('Disconnecting from Live Market...');
    this.connected = false;
    
    if (this.marketDataUpdateInterval) {
      clearInterval(this.marketDataUpdateInterval);
      this.marketDataUpdateInterval = null;
    }
    
    return { success: true, data: true, message: 'Disconnected from live market' };
  }

  async getCurrentPrice(symbol: string): Promise<BrokerResponse<number>> {
    const marketDataArray = await marketDataService.getMarketData([symbol]);
    if (!marketDataArray || marketDataArray.length === 0) {
      return { success: false, error: `Unable to fetch price for ${symbol}` };
    }
    
    return { success: true, data: marketDataArray[0].price };
  }

  async getMarketData(symbol: string): Promise<BrokerResponse<any>> {
    const marketDataArray = await marketDataService.getMarketData([symbol]);
    if (!marketDataArray || marketDataArray.length === 0) {
      return { success: false, error: `Market data not available for ${symbol}` };
    }

    const marketData = marketDataArray[0];
    return {
      success: true,
      data: {
        symbol: marketData.symbol,
        price: marketData.price,
        bid: marketData.price - 0.5,
        ask: marketData.price + 0.5,
        change: marketData.change,
        changePercent: marketData.changePercent,
        volume: marketData.volume,
        timestamp: marketData.timestamp,
        source: marketData.source
      }
    };
  }

  // Paper trading implementation for orders (since we can't actually trade)
  async placeOrder(orderRequest: OrderRequest): Promise<BrokerResponse<Order>> {
    if (!this.connected) {
      return { success: false, error: 'Broker not connected' };
    }

    const priceResult = await this.getCurrentPrice(orderRequest.symbol);
    if (!priceResult.success) {
      return { success: false, error: `Unable to get current price for ${orderRequest.symbol}` };
    }

    const currentPrice = priceResult.data!;
    const orderPrice = orderRequest.price || currentPrice;

    // Create order
    const order: Order = {
      id: `LIVE_ORDER_${this.orderCounter++}`,
      symbol: orderRequest.symbol,
      side: orderRequest.side,
      type: orderRequest.type,
      quantity: orderRequest.quantity,
      price: orderRequest.price,
      stopPrice: orderRequest.stopPrice,
      timeInForce: orderRequest.timeInForce || 'GTC',
      status: 'PENDING',
      filledQuantity: 0,
      timestamp: new Date(),
      updatedAt: new Date(),
      fees: 0
    };

    this.account.orders.push(order);

    // Simulate execution for market orders with live prices
    if (orderRequest.type === 'MARKET') {
      await this.executeOrder(order.id, currentPrice);
    } else if (orderRequest.type === 'LIMIT') {
      // Check if limit order can be filled immediately
      const canFill = (orderRequest.side === 'BUY' && currentPrice <= orderPrice) ||
                      (orderRequest.side === 'SELL' && currentPrice >= orderPrice);
      
      if (canFill) {
        await this.executeOrder(order.id, orderPrice);
      }
    }

    console.log(`Live order placed: ${order.id} - ${order.symbol} ${order.side} ${order.quantity} @ ₹${orderPrice}`);
    return { success: true, data: order };
  }

  async cancelOrder(orderId: string): Promise<BrokerResponse<boolean>> {
    const orderIndex = this.account.orders.findIndex(o => o.id === orderId);
    if (orderIndex === -1) {
      return { success: false, error: 'Order not found' };
    }

    const order = this.account.orders[orderIndex];
    if (order.status === 'FILLED') {
      return { success: false, error: 'Cannot cancel filled order' };
    }

    order.status = 'CANCELLED';
    order.updatedAt = new Date();

    console.log(`Order cancelled: ${orderId}`);
    return { success: true, data: true };
  }

  async getAccount(): Promise<BrokerResponse<BrokerAccount>> {
    await this.updateAccountMetrics();
    return { success: true, data: this.account };
  }

  async refreshAccount(): Promise<BrokerResponse<BrokerAccount>> {
    await this.updateAccountMetrics();
    return { success: true, data: this.account };
  }

  async getOrder(orderId: string): Promise<BrokerResponse<Order>> {
    const order = this.account.orders.find(o => o.id === orderId);
    if (!order) {
      return { success: false, error: 'Order not found' };
    }
    return { success: true, data: order };
  }

  async getOrders(symbol?: string): Promise<BrokerResponse<Order[]>> {
    let orders = this.account.orders;
    if (symbol) {
      orders = orders.filter(o => o.symbol === symbol);
    }
    return { success: true, data: orders };
  }

  async getPositions(symbol?: string): Promise<BrokerResponse<BrokerPosition[]>> {
    let positions = this.account.positions;
    if (symbol) {
      positions = positions.filter(p => p.symbol === symbol);
    }
    return { success: true, data: positions };
  }

  async closePosition(symbol: string, quantity?: number): Promise<BrokerResponse<Order>> {
    const position = this.account.positions.find(p => p.symbol === symbol);
    if (!position) {
      return { success: false, error: 'Position not found' };
    }

    const closeQuantity = quantity || position.quantity;
    const closeSide = position.side === 'LONG' ? 'SELL' : 'BUY';

    return this.placeOrder({
      symbol,
      side: closeSide,
      type: 'MARKET',
      quantity: closeQuantity
    });
  }

  async getTrades(symbol?: string, limit?: number): Promise<BrokerResponse<Trade[]>> {
    let trades = this.account.trades;
    if (symbol) {
      trades = trades.filter(t => t.symbol === symbol);
    }
    if (limit) {
      trades = trades.slice(-limit);
    }
    return { success: true, data: trades };
  }

  async modifyOrder(orderId: string, updates: Partial<OrderRequest>): Promise<BrokerResponse<Order>> {
    const order = this.account.orders.find(o => o.id === orderId);
    if (!order) {
      return { success: false, error: 'Order not found' };
    }

    if (order.status === 'FILLED' || order.status === 'CANCELLED') {
      return { success: false, error: 'Cannot modify filled or cancelled order' };
    }

    // Update order properties
    if (updates.price !== undefined) order.price = updates.price;
    if (updates.quantity !== undefined) order.quantity = updates.quantity;
    if (updates.stopPrice !== undefined) order.stopPrice = updates.stopPrice;
    
    order.updatedAt = new Date();

    return { success: true, data: order };
  }

  private startMarketDataUpdates(): void {
    this.marketDataUpdateInterval = setInterval(async () => {
      // Update positions with live prices
      for (const position of this.account.positions) {
        const priceResult = await this.getCurrentPrice(position.symbol);
        if (priceResult.success) {
          position.currentPrice = priceResult.data!;
        }
      }
      
      // Check pending limit orders against live prices
      await this.checkPendingOrders();
    }, 5000); // Update every 5 seconds
  }

  private async executeOrder(orderId: string, executionPrice: number): Promise<void> {
    const order = this.account.orders.find(o => o.id === orderId);
    if (!order) return;

    // Calculate fees (0.1% of trade value)
    const tradeValue = order.quantity * executionPrice;
    const fees = tradeValue * 0.001;

    // Update order
    order.status = 'FILLED';
    order.filledQuantity = order.quantity;
    order.filledPrice = executionPrice;
    order.fees = fees;
    order.updatedAt = new Date();

    // Create trade record
    const trade: Trade = {
      id: `LIVE_TRADE_${this.tradeCounter++}`,
      orderId: order.id,
      symbol: order.symbol,
      side: order.side,
      quantity: order.quantity,
      price: executionPrice,
      fees,
      timestamp: new Date()
    };

    this.account.trades.push(trade);

    // Update positions
    await this.updatePosition(order.symbol, order.side, order.quantity, executionPrice);

    // Update account balance
    const totalCost = tradeValue + fees;
    if (order.side === 'BUY') {
      this.account.availableBalance -= totalCost;
    } else {
      this.account.availableBalance += tradeValue - fees;
    }

    console.log(`Live order executed: ${orderId} - ${order.quantity} shares at ₹${executionPrice}`);
  }

  private async updatePosition(symbol: string, side: 'BUY' | 'SELL', quantity: number, price: number): Promise<void> {
    let position = this.account.positions.find(p => p.symbol === symbol);

    if (!position) {
      // Create new position
      position = {
        symbol,
        side: side === 'BUY' ? 'LONG' : 'SHORT',
        quantity: side === 'BUY' ? quantity : -quantity,
        averagePrice: price,
        currentPrice: price,
        unrealizedPnL: 0,
        realizedPnL: 0,
        marketValue: quantity * price,
        timestamp: new Date()
      };
      this.account.positions.push(position);
    } else {
      // Update existing position
      if ((position.side === 'LONG' && side === 'BUY') || (position.side === 'SHORT' && side === 'SELL')) {
        // Increase position
        const totalValue = (position.quantity * position.averagePrice) + (quantity * price);
        position.quantity += side === 'BUY' ? quantity : -quantity;
        position.averagePrice = totalValue / Math.abs(position.quantity);
      } else {
        // Reduce position
        position.quantity -= side === 'BUY' ? quantity : -quantity;
        if (position.quantity === 0) {
          // Close position
          const positionIndex = this.account.positions.findIndex(p => p.symbol === symbol);
          this.account.positions.splice(positionIndex, 1);
        }
      }
      
      if (position.quantity !== 0) {
        position.marketValue = Math.abs(position.quantity) * price;
        position.currentPrice = price;
      }
    }
  }

  private async updateAccountMetrics(): Promise<void> {
    let totalUnrealizedPnL = 0;
    let totalMarketValue = 0;

    // Update position metrics with live prices
    for (const position of this.account.positions) {
      const priceResult = await this.getCurrentPrice(position.symbol);
      if (priceResult.success) {
        const currentPrice = priceResult.data!;
        position.currentPrice = currentPrice;
        
        const pnl = (currentPrice - position.averagePrice) * position.quantity;
        position.unrealizedPnL = position.side === 'LONG' ? pnl : -pnl;
        position.marketValue = Math.abs(position.quantity) * currentPrice;
        
        totalUnrealizedPnL += position.unrealizedPnL;
        totalMarketValue += position.marketValue;
      }
    }

    this.account.unrealizedPnL = totalUnrealizedPnL;
    this.account.equity = this.account.balance + totalUnrealizedPnL;
    this.account.usedMargin = totalMarketValue * 0.2; // 20% margin requirement
    this.account.freeMargin = this.account.availableBalance - this.account.usedMargin;
    this.account.marginLevel = this.account.usedMargin > 0 ? (this.account.equity / this.account.usedMargin) * 100 : 0;
  }

  private async checkPendingOrders(): Promise<void> {
    const pendingOrders = this.account.orders.filter(o => o.status === 'PENDING');
    
    for (const order of pendingOrders) {
      const priceResult = await this.getCurrentPrice(order.symbol);
      if (!priceResult.success || !order.price) continue;

      const currentPrice = priceResult.data!;
      const shouldExecute = 
        (order.type === 'LIMIT' && order.side === 'BUY' && currentPrice <= order.price) ||
        (order.type === 'LIMIT' && order.side === 'SELL' && currentPrice >= order.price) ||
        (order.type === 'STOP' && order.stopPrice && 
         ((order.side === 'BUY' && currentPrice >= order.stopPrice) ||
          (order.side === 'SELL' && currentPrice <= order.stopPrice)));

      if (shouldExecute) {
        await this.executeOrder(order.id, order.type === 'LIMIT' ? order.price : currentPrice);
      }
    }
  }
}

import React, { useState } from 'react';
import { Dialog, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, CreditCard, Smartphone, Building2, AlertTriangle, Shield } from 'lucide-react';
import { paymentCardSchema, sanitizeString, sanitizeNumericInput, checkRateLimit } from '@/lib/security';
import { useToast } from '@/hooks/use-toast';

interface PaymentModalProps {
  open: boolean;
  onClose: () => void;
  onPaymentSuccess: () => void;
}

const PaymentModal = ({ open, onClose, onPaymentSuccess }: PaymentModalProps) => {
  const [paymentMethod, setPaymentMethod] = useState<'card' | 'upi' | 'netbanking'>('card');
  const [cardForm, setCardForm] = useState({
    number: '',
    expiry: '',
    cvv: '',
    name: ''
  });
  const [upiId, setUpiId] = useState('');
  const [processing, setProcessing] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const { toast } = useToast();

  const handlePayment = async () => {
    if (!checkRateLimit('payment_attempt', 3, 300000)) { // 3 attempts per 5 minutes
      toast({
        title: "Rate Limited",
        description: "Too many payment attempts. Please wait 5 minutes.",
        variant: "destructive",
      });
      return;
    }

    setValidationErrors({});
    
    // Validate based on payment method
    if (paymentMethod === 'card') {
      const sanitizedCard = {
        number: sanitizeNumericInput(cardForm.number),
        expiry: sanitizeString(cardForm.expiry),
        cvv: sanitizeNumericInput(cardForm.cvv),
        name: sanitizeString(cardForm.name)
      };

      const validation = paymentCardSchema.safeParse(sanitizedCard);
      if (!validation.success) {
        const errors: Record<string, string> = {};
        validation.error.errors.forEach((error) => {
          if (error.path[0]) {
            errors[error.path[0].toString()] = error.message;
          }
        });
        setValidationErrors(errors);
        return;
      }
    } else if (paymentMethod === 'upi') {
      const sanitizedUpi = sanitizeString(upiId);
      if (!sanitizedUpi.includes('@') || sanitizedUpi.length < 5) {
        setValidationErrors({ upi: 'Please enter a valid UPI ID' });
        return;
      }
    }

    setProcessing(true);
    
    try {
      // Simulate secure payment processing
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      toast({
        title: "Payment Successful",
        description: "Your subscription has been activated!",
      });
      
      onPaymentSuccess();
      onClose();
    } catch (error) {
      toast({
        title: "Payment Failed",
        description: "Please check your details and try again.",
        variant: "destructive",
      });
    } finally {
      setProcessing(false);
    }
  };

  const formatCardNumber = (value: string) => {
    const v = sanitizeNumericInput(value);
    const matches = v.match(/\d{4,16}/g);
    const match = matches && matches[0] || '';
    const parts = [];
    
    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4));
    }
    
    if (parts.length) {
      return parts.join(' ');
    } else {
      return v;
    }
  };

  const formatExpiry = (value: string) => {
    let sanitized = sanitizeNumericInput(value);
    if (sanitized.length >= 2) {
      sanitized = sanitized.substring(0, 2) + '/' + sanitized.substring(2, 4);
    }
    return sanitized;
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-lg bg-slate-900 border-slate-700 text-white">
        <DialogHeader>
          <DialogTitle className="text-center text-2xl font-bold flex items-center justify-center gap-2">
            <Shield className="w-5 h-5 text-green-400" />
            Complete Your Subscription
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Security Notice */}
          <Alert className="border-green-500/20 bg-green-900/10">
            <Shield className="h-4 w-4 text-green-400" />
            <AlertDescription className="text-green-300">
              🔒 Your payment information is processed securely and never stored on our servers.
            </AlertDescription>
          </Alert>

          {/* Plan Summary */}
          <Card className="bg-gradient-to-r from-blue-900/30 to-purple-900/30 border-slate-600">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg">Professional Plan</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between items-center">
                <span>Monthly Subscription</span>
                <span className="text-2xl font-bold">₹4,999</span>
              </div>
              <div className="flex flex-wrap gap-2">
                <Badge variant="secondary" className="bg-green-900/30 text-green-400">
                  <CheckCircle className="w-3 h-3 mr-1" />
                  7-Day Free Trial
                </Badge>
                <Badge variant="secondary" className="bg-blue-900/30 text-blue-400">
                  Cancel Anytime
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* Payment Methods */}
          <div className="space-y-4">
            <h3 className="font-semibold">Choose Payment Method</h3>
            <div className="grid grid-cols-3 gap-2">
              <Button
                variant={paymentMethod === 'card' ? 'default' : 'outline'}
                onClick={() => setPaymentMethod('card')}
                className={`h-16 ${paymentMethod === 'card' ? 'bg-blue-600' : 'border-slate-600'}`}
              >
                <div className="text-center">
                  <CreditCard className="w-5 h-5 mx-auto mb-1" />
                  <div className="text-xs">Card</div>
                </div>
              </Button>
              <Button
                variant={paymentMethod === 'upi' ? 'default' : 'outline'}
                onClick={() => setPaymentMethod('upi')}
                className={`h-16 ${paymentMethod === 'upi' ? 'bg-blue-600' : 'border-slate-600'}`}
              >
                <div className="text-center">
                  <Smartphone className="w-5 h-5 mx-auto mb-1" />
                  <div className="text-xs">UPI</div>
                </div>
              </Button>
              <Button
                variant={paymentMethod === 'netbanking' ? 'default' : 'outline'}
                onClick={() => setPaymentMethod('netbanking')}
                className={`h-16 ${paymentMethod === 'netbanking' ? 'bg-blue-600' : 'border-slate-600'}`}
              >
                <div className="text-center">
                  <Building2 className="w-5 h-5 mx-auto mb-1" />
                  <div className="text-xs">Banking</div>
                </div>
              </Button>
            </div>
          </div>

          {/* Payment Forms with Enhanced Security */}
          {paymentMethod === 'card' && (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="card-number">Card Number</Label>
                <Input
                  id="card-number"
                  placeholder="1234 5678 9012 3456"
                  value={cardForm.number}
                  onChange={(e) => setCardForm({ ...cardForm, number: formatCardNumber(e.target.value) })}
                  className="bg-slate-800 border-slate-600"
                  maxLength={19}
                />
                {validationErrors.number && (
                  <Alert className="border-red-500/20 bg-red-900/10">
                    <AlertTriangle className="h-4 w-4 text-red-400" />
                    <AlertDescription className="text-red-300 text-sm">
                      {validationErrors.number}
                    </AlertDescription>
                  </Alert>
                )}
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="expiry">Expiry Date</Label>
                  <Input
                    id="expiry"
                    placeholder="MM/YY"
                    value={cardForm.expiry}
                    onChange={(e) => setCardForm({ ...cardForm, expiry: formatExpiry(e.target.value) })}
                    className="bg-slate-800 border-slate-600"
                    maxLength={5}
                  />
                  {validationErrors.expiry && (
                    <Alert className="border-red-500/20 bg-red-900/10">
                      <AlertTriangle className="h-4 w-4 text-red-400" />
                      <AlertDescription className="text-red-300 text-sm">
                        {validationErrors.expiry}
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="cvv">CVV</Label>
                  <Input
                    id="cvv"
                    placeholder="123"
                    value={cardForm.cvv}
                    onChange={(e) => setCardForm({ ...cardForm, cvv: sanitizeNumericInput(e.target.value) })}
                    className="bg-slate-800 border-slate-600"
                    maxLength={4}
                    type="password"
                  />
                  {validationErrors.cvv && (
                    <Alert className="border-red-500/20 bg-red-900/10">
                      <AlertTriangle className="h-4 w-4 text-red-400" />
                      <AlertDescription className="text-red-300 text-sm">
                        {validationErrors.cvv}
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="card-name">Cardholder Name</Label>
                <Input
                  id="card-name"
                  placeholder="John Doe"
                  value={cardForm.name}
                  onChange={(e) => setCardForm({ ...cardForm, name: sanitizeString(e.target.value) })}
                  className="bg-slate-800 border-slate-600"
                />
                {validationErrors.name && (
                  <Alert className="border-red-500/20 bg-red-900/10">
                    <AlertTriangle className="h-4 w-4 text-red-400" />
                    <AlertDescription className="text-red-300 text-sm">
                      {validationErrors.name}
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            </div>
          )}

          {/* UPI Form with validation */}
          {paymentMethod === 'upi' && (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="upi-id">UPI ID</Label>
                <Input
                  id="upi-id"
                  placeholder="example@paytm"
                  value={upiId}
                  onChange={(e) => setUpiId(sanitizeString(e.target.value))}
                  className="bg-slate-800 border-slate-600"
                />
                {validationErrors.upi && (
                  <Alert className="border-red-500/20 bg-red-900/10">
                    <AlertTriangle className="h-4 w-4 text-red-400" />
                    <AlertDescription className="text-red-300 text-sm">
                      {validationErrors.upi}
                    </AlertDescription>
                  </Alert>
                )}
              </div>
              <div className="bg-slate-800/50 p-4 rounded-lg">
                <h4 className="font-medium mb-2">Popular UPI Apps</h4>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>• Google Pay</div>
                  <div>• PhonePe</div>
                  <div>• Paytm</div>
                  <div>• BHIM</div>
                </div>
              </div>
            </div>
          )}

          {/* ... keep existing code (net banking section) the same ... */}

          {/* Payment Button */}
          <Button
            onClick={handlePayment}
            disabled={processing}
            className="w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-lg py-3 h-auto"
          >
            {processing ? (
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                Processing Secure Payment...
              </div>
            ) : (
              `Pay ₹4,999 & Start Trial`
            )}
          </Button>

          <div className="text-center text-sm text-slate-400 flex items-center justify-center gap-2">
            <Shield className="w-4 h-4 text-green-400" />
            Secured by 256-bit SSL encryption
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default PaymentModal;

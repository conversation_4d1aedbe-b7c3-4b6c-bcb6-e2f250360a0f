
import { generateSecureKey } from '@/lib/security';

interface StorageItem {
  value: string;
  encrypted: boolean;
  timestamp: number;
  expires?: number;
}

class SecureStorageService {
  private encryptionKey: string;

  constructor() {
    this.encryptionKey = this.getOrCreateEncryptionKey();
  }

  private getOrCreateEncryptionKey(): string {
    let key = localStorage.getItem('app_encryption_key');
    if (!key) {
      key = generateSecureKey();
      localStorage.setItem('app_encryption_key', key);
    }
    return key;
  }

  private async simpleEncrypt(text: string, key: string): Promise<string> {
    // Simple XOR encryption for demo purposes
    // In production, use proper encryption like AES
    const encoder = new TextEncoder();
    const data = encoder.encode(text);
    const keyData = encoder.encode(key);
    
    const encrypted = new Uint8Array(data.length);
    for (let i = 0; i < data.length; i++) {
      encrypted[i] = data[i] ^ keyData[i % keyData.length];
    }
    
    return btoa(String.fromCharCode(...encrypted));
  }

  private async simpleDecrypt(encryptedText: string, key: string): Promise<string> {
    try {
      const data = new Uint8Array(atob(encryptedText).split('').map(c => c.charCodeAt(0)));
      const encoder = new TextEncoder();
      const keyData = encoder.encode(key);
      
      const decrypted = new Uint8Array(data.length);
      for (let i = 0; i < data.length; i++) {
        decrypted[i] = data[i] ^ keyData[i % keyData.length];
      }
      
      return new TextDecoder().decode(decrypted);
    } catch {
      throw new Error('Failed to decrypt data');
    }
  }

  async setSecureItem(key: string, value: string, encrypt: boolean = true, ttl?: number): Promise<void> {
    const item: StorageItem = {
      value: encrypt ? await this.simpleEncrypt(value, this.encryptionKey) : value,
      encrypted: encrypt,
      timestamp: Date.now(),
      expires: ttl ? Date.now() + ttl : undefined
    };

    localStorage.setItem(`secure_${key}`, JSON.stringify(item));
  }

  async getSecureItem(key: string): Promise<string | null> {
    const stored = localStorage.getItem(`secure_${key}`);
    if (!stored) return null;

    try {
      const item: StorageItem = JSON.parse(stored);
      
      // Check expiration
      if (item.expires && Date.now() > item.expires) {
        this.removeSecureItem(key);
        return null;
      }

      if (item.encrypted) {
        return await this.simpleDecrypt(item.value, this.encryptionKey);
      }
      
      return item.value;
    } catch (error) {
      console.error('Failed to retrieve secure item:', error);
      this.removeSecureItem(key);
      return null;
    }
  }

  removeSecureItem(key: string): void {
    localStorage.removeItem(`secure_${key}`);
  }

  clearAllSecureItems(): void {
    const keys = Object.keys(localStorage).filter(key => key.startsWith('secure_'));
    keys.forEach(key => localStorage.removeItem(key));
  }
}

export const secureStorage = new SecureStorageService();


import { useState, useEffect } from 'react';
import { LiveBroker } from '@/services/LiveBroker';
import { BrokerAccount } from '@/types/broker';

export const useBrokerConnection = () => {
  const [broker] = useState(() => new LiveBroker());
  const [brokerAccount, setBrokerAccount] = useState<BrokerAccount | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionMessage, setConnectionMessage] = useState<string>('');

  // Initialize broker connection
  useEffect(() => {
    const initializeBroker = async () => {
      try {
        console.log('Initializing live broker connection...');
        const result = await broker.connect();
        if (result.success) {
          setIsConnected(true);
          setConnectionMessage(result.message || 'Connected to live market data');
          const accountResult = await broker.getAccount();
          if (accountResult.success) {
            setBrokerAccount(accountResult.data);
          }
        } else {
          setConnectionMessage(result.error || 'Failed to connect to market data');
        }
      } catch (error) {
        console.error('Failed to initialize live broker:', error);
        setConnectionMessage('Failed to initialize live broker');
      }
    };

    initializeBroker();
  }, [broker]);

  // Refresh broker account periodically
  useEffect(() => {
    if (!isConnected) return;

    const refreshAccount = async () => {
      try {
        const result = await broker.refreshAccount();
        if (result.success) {
          setBrokerAccount(result.data);
        }
      } catch (error) {
        console.error('Error refreshing live account:', error);
      }
    };

    const interval = setInterval(refreshAccount, 15000); // Every 15 seconds for live data
    return () => clearInterval(interval);
  }, [broker, isConnected]);

  return {
    broker,
    brokerAccount,
    setBrokerAccount,
    isConnected,
    connectionMessage
  };
};

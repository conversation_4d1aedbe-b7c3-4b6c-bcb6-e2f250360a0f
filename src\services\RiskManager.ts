
import { AccountInfo, RiskParameters, TradeRequest, RiskValidationResult, Position } from '@/types/trading';
import { PositionSizingCalculator } from './PositionSizingCalculator';

export class RiskManager {
  private account: AccountInfo;
  private riskParams: RiskParameters;
  private calculator: PositionSizingCalculator;

  constructor(account: AccountInfo, riskParams: RiskParameters) {
    this.account = account;
    this.riskParams = riskParams;
    this.calculator = new PositionSizingCalculator(account, riskParams);
  }

  /**
   * Validate a trade request against all risk parameters
   */
  validateTrade(trade: TradeRequest, sector: string = 'Unknown'): RiskValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check if trading is halted due to daily loss limit
    if (this.isDailyLossLimitReached()) {
      errors.push('Daily loss limit reached. Trading is halted.');
    }

    // Check maximum drawdown
    if (this.isMaxDrawdownReached()) {
      errors.push('Maximum drawdown limit reached. Trading is halted.');
    }

    // Validate stop loss
    if (!trade.stopLoss) {
      errors.push('Stop loss is required for all trades.');
    }

    // Validate risk-reward ratio
    if (trade.stopLoss && trade.takeProfit) {
      const riskRewardRatio = this.calculateRiskRewardRatio(trade);
      if (riskRewardRatio < this.riskParams.minRiskRewardRatio) {
        errors.push(`Risk-reward ratio (${riskRewardRatio.toFixed(2)}) is below minimum (${this.riskParams.minRiskRewardRatio})`);
      }
    }

    // Check available balance
    const positionValue = trade.quantity * trade.price;
    if (positionValue > this.account.availableBalance) {
      errors.push('Insufficient available balance for this trade.');
    }

    // Check position sizing
    let suggestedQuantity: number | undefined;
    let riskAmount = 0;

    if (trade.stopLoss) {
      try {
        const positionSize = this.calculator.calculateFixedPercentageSize(
          trade.price,
          trade.stopLoss
        );
        
        riskAmount = positionSize.riskAmount;
        
        if (trade.quantity > positionSize.quantity) {
          warnings.push(`Suggested quantity: ${positionSize.quantity} (current: ${trade.quantity})`);
          suggestedQuantity = positionSize.quantity;
        }
      } catch (error) {
        errors.push(`Position sizing error: ${error}`);
      }
    }

    // Check sector and single stock exposure
    const exposureCheck = this.checkExposureLimits(trade.symbol, sector, positionValue);
    errors.push(...exposureCheck.errors);
    warnings.push(...exposureCheck.warnings);

    // Check portfolio risk
    const portfolioRisk = this.calculatePortfolioRisk();
    if (portfolioRisk > this.riskParams.maxPortfolioRisk) {
      warnings.push(`Portfolio risk (${portfolioRisk.toFixed(2)}%) exceeds limit (${this.riskParams.maxPortfolioRisk}%)`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestedQuantity,
      riskAmount,
      positionSize: trade.quantity * trade.price
    };
  }

  /**
   * Check if daily loss limit is reached
   */
  private isDailyLossLimitReached(): boolean {
    return this.account.dailyPnL <= -this.riskParams.dailyLossLimit;
  }

  /**
   * Check if maximum drawdown is reached
   */
  private isMaxDrawdownReached(): boolean {
    const drawdown = (this.account.totalPnL / this.account.totalBalance) * 100;
    return drawdown <= -this.riskParams.maxDrawdown;
  }

  /**
   * Calculate risk-reward ratio for a trade
   */
  private calculateRiskRewardRatio(trade: TradeRequest): number {
    if (!trade.stopLoss || !trade.takeProfit) return 0;
    
    const risk = Math.abs(trade.price - trade.stopLoss);
    const reward = Math.abs(trade.takeProfit - trade.price);
    
    return reward / risk;
  }

  /**
   * Check exposure limits for sector and single stock
   */
  private checkExposureLimits(symbol: string, sector: string, positionValue: number) {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Current single stock exposure
    const currentStockExposure = this.account.positions
      .filter(pos => pos.symbol === symbol)
      .reduce((sum, pos) => sum + pos.marketValue, 0);

    const totalStockExposure = currentStockExposure + positionValue;
    const stockExposurePercent = (totalStockExposure / this.account.totalBalance) * 100;

    if (stockExposurePercent > this.riskParams.maxSingleStockExposure) {
      errors.push(`Single stock exposure (${stockExposurePercent.toFixed(2)}%) exceeds limit (${this.riskParams.maxSingleStockExposure}%)`);
    }

    // Current sector exposure
    const currentSectorExposure = this.account.positions
      .filter(pos => pos.sector === sector)
      .reduce((sum, pos) => sum + pos.marketValue, 0);

    const totalSectorExposure = currentSectorExposure + positionValue;
    const sectorExposurePercent = (totalSectorExposure / this.account.totalBalance) * 100;

    if (sectorExposurePercent > this.riskParams.maxSectorExposure) {
      errors.push(`Sector exposure (${sectorExposurePercent.toFixed(2)}%) exceeds limit (${this.riskParams.maxSectorExposure}%)`);
    }

    return { errors, warnings };
  }

  /**
   * Calculate current portfolio risk
   */
  private calculatePortfolioRisk(): number {
    const totalRisk = this.account.positions.reduce((sum, pos) => {
      // Estimate risk as 2% of position value (assuming 2% stop loss)
      return sum + (pos.marketValue * 0.02);
    }, 0);

    return (totalRisk / this.account.totalBalance) * 100;
  }

  /**
   * Get current risk metrics summary
   */
  getRiskMetrics() {
    const portfolioRisk = this.calculatePortfolioRisk();
    const drawdown = (this.account.totalPnL / this.account.totalBalance) * 100;
    const dailyLossRemaining = this.riskParams.dailyLossLimit + this.account.dailyPnL;

    return {
      portfolioRisk,
      drawdown,
      dailyLossRemaining,
      tradingHalted: this.isDailyLossLimitReached() || this.isMaxDrawdownReached(),
      marginUsed: (this.account.usedMargin / this.account.totalBalance) * 100
    };
  }
}

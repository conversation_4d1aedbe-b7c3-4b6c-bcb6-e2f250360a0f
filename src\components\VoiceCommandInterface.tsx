import React, { useState, useCallback, useRef } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Mi<PERSON>, MicOff, Volume2, VolumeX, Loader2 } from 'lucide-react';
import { GoogleCloudService } from '@/services/GoogleCloudService';
import { useToast } from '@/hooks/use-toast';

interface VoiceCommandInterfaceProps {
  onCommand?: (command: string, params: any) => void;
}

interface VoiceCommand {
  pattern: RegExp;
  action: string;
  description: string;
  example: string;
}

const VOICE_COMMANDS: VoiceCommand[] = [
  {
    pattern: /show\s+(\w+)/i,
    action: 'SHOW_CHART',
    description: 'Show chart for symbol',
    example: 'Show RELIANCE'
  },
  {
    pattern: /set\s+alert\s+(\w+)\s+at\s+(\d+)/i,
    action: 'SET_ALERT',
    description: 'Set price alert',
    example: 'Set alert RELIANCE at 2500'
  },
  {
    pattern: /buy\s+(\d+)\s+(\w+)/i,
    action: 'BUY',
    description: 'Place buy order',
    example: 'Buy 100 RELIANCE'
  },
  {
    pattern: /sell\s+(\d+)\s+(\w+)/i,
    action: 'SELL',
    description: 'Place sell order',
    example: 'Sell 50 TCS'
  },
  {
    pattern: /news\s+(\w+)/i,
    action: 'GET_NEWS',
    description: 'Get news for symbol',
    example: 'News HDFCBANK'
  },
  {
    pattern: /(portfolio|holdings)/i,
    action: 'SHOW_PORTFOLIO',
    description: 'Show portfolio',
    example: 'Portfolio'
  },
  {
    pattern: /market\s+status/i,
    action: 'MARKET_STATUS',
    description: 'Check market status',
    example: 'Market status'
  }
];

export const VoiceCommandInterface: React.FC<VoiceCommandInterfaceProps> = ({ onCommand }) => {
  const [isListening, setIsListening] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [lastCommand, setLastCommand] = useState<string>('');
  const [audioLevel, setAudioLevel] = useState(0);
  const [isSpeaking, setIsSpeaking] = useState(false);
  
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const { toast } = useToast();

  const startListening = useCallback(async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream, { mimeType: 'audio/webm' });
      
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];
      
      mediaRecorder.ondataavailable = (event) => {
        audioChunksRef.current.push(event.data);
      };
      
      mediaRecorder.onstop = async () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });
        await processAudio(audioBlob);
        stream.getTracks().forEach(track => track.stop());
      };
      
      // Monitor audio level
      const audioContext = new AudioContext();
      const analyser = audioContext.createAnalyser();
      const microphone = audioContext.createMediaStreamSource(stream);
      const dataArray = new Uint8Array(analyser.frequencyBinCount);
      
      microphone.connect(analyser);
      
      const updateAudioLevel = () => {
        if (isListening) {
          analyser.getByteFrequencyData(dataArray);
          const average = dataArray.reduce((sum, value) => sum + value, 0) / dataArray.length;
          setAudioLevel(average);
          requestAnimationFrame(updateAudioLevel);
        }
      };
      updateAudioLevel();
      
      mediaRecorder.start();
      setIsListening(true);
      
      toast({
        title: "Voice Command Active",
        description: "Listening for voice commands...",
      });
      
    } catch (error) {
      console.error('Error starting voice recognition:', error);
      toast({
        title: "Microphone Error",
        description: "Could not access microphone. Please check permissions.",
        variant: "destructive"
      });
    }
  }, [isListening, toast]);

  const stopListening = useCallback(() => {
    if (mediaRecorderRef.current && isListening) {
      mediaRecorderRef.current.stop();
      setIsListening(false);
      setAudioLevel(0);
    }
  }, [isListening]);

  const processAudio = async (audioBlob: Blob) => {
    setIsProcessing(true);
    
    try {
      // Convert blob to base64
      const arrayBuffer = await audioBlob.arrayBuffer();
      const base64Audio = btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)));
      
      // Use Google Cloud Speech-to-Text
      const transcript = await GoogleCloudService.speechToText(base64Audio);
      
      if (transcript) {
        setLastCommand(transcript);
        await processVoiceCommand(transcript);
      } else {
        toast({
          title: "No Speech Detected",
          description: "Could not understand the voice command. Please try again.",
          variant: "destructive"
        });
      }
      
    } catch (error) {
      console.error('Error processing audio:', error);
      toast({
        title: "Processing Error",
        description: "Failed to process voice command. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const processVoiceCommand = async (transcript: string) => {
    const cleanTranscript = transcript.toLowerCase().trim();
    
    for (const command of VOICE_COMMANDS) {
      const match = cleanTranscript.match(command.pattern);
      if (match) {
        const params = match.slice(1); // Remove the full match, keep capture groups
        
        toast({
          title: "Command Recognized",
          description: `Executing: ${command.description}`,
        });
        
        // Speak confirmation
        await speakText(`Executing ${command.description}`);
        
        // Execute command
        onCommand?.(command.action, params);
        return;
      }
    }
    
    // No command matched
    toast({
      title: "Command Not Recognized",
      description: "Please try one of the available voice commands.",
      variant: "destructive"
    });
    
    await speakText("Command not recognized. Please try again.");
  };

  const speakText = async (text: string) => {
    try {
      setIsSpeaking(true);
      
      // Use Google Cloud Text-to-Speech
      const audioContent = await GoogleCloudService.textToSpeech(text);
      
      // Create audio element and play
      const audio = new Audio(`data:audio/mp3;base64,${audioContent}`);
      audio.onended = () => setIsSpeaking(false);
      await audio.play();
      
    } catch (error) {
      console.error('Error with text-to-speech:', error);
      // Fallback to browser's speech synthesis
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.onend = () => setIsSpeaking(false);
      speechSynthesis.speak(utterance);
    }
  };

  const toggleListening = () => {
    if (isListening) {
      stopListening();
    } else {
      startListening();
    }
  };

  return (
    <Card className="w-full backdrop-blur-md bg-card/50 border-primary/20">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Volume2 className="h-5 w-5" />
          Voice Commands
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Voice Control Button */}
        <div className="flex items-center justify-center gap-4">
          <Button
            onClick={toggleListening}
            size="lg"
            variant={isListening ? "destructive" : "default"}
            disabled={isProcessing}
            className="relative overflow-hidden"
          >
            {isProcessing ? (
              <>
                <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                Processing...
              </>
            ) : isListening ? (
              <>
                <MicOff className="h-5 w-5 mr-2" />
                Stop Listening
              </>
            ) : (
              <>
                <Mic className="h-5 w-5 mr-2" />
                Start Voice Command
              </>
            )}
            
            {/* Audio level indicator */}
            {isListening && (
              <div 
                className="absolute bottom-0 left-0 bg-accent/30 transition-all duration-100"
                style={{ width: `${(audioLevel / 255) * 100}%`, height: '2px' }}
              />
            )}
          </Button>
          
          {isSpeaking && (
            <Badge variant="secondary" className="animate-pulse">
              <VolumeX className="h-3 w-3 mr-1" />
              Speaking
            </Badge>
          )}
        </div>

        {/* Last Command */}
        {lastCommand && (
          <div className="p-3 bg-muted rounded-lg">
            <p className="text-sm text-muted-foreground">Last Command:</p>
            <p className="font-medium">{lastCommand}</p>
          </div>
        )}

        {/* Available Commands */}
        <div className="space-y-2">
          <p className="text-sm font-medium">Available Commands:</p>
          <div className="grid grid-cols-1 gap-2 max-h-40 overflow-y-auto">
            {VOICE_COMMANDS.map((command, index) => (
              <div key={index} className="p-2 bg-muted/50 rounded text-xs">
                <p className="font-medium">{command.description}</p>
                <p className="text-muted-foreground italic">"{command.example}"</p>
              </div>
            ))}
          </div>
        </div>

        {/* Status Indicators */}
        <div className="flex gap-2 flex-wrap">
          <Badge variant={isListening ? "default" : "secondary"}>
            {isListening ? "Listening" : "Idle"}
          </Badge>
          {isProcessing && (
            <Badge variant="outline">Processing</Badge>
          )}
          {isSpeaking && (
            <Badge variant="outline">Speaking</Badge>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
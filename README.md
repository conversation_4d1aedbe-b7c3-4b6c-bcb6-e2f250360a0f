# ElectroRide – AI‑Powered Workflow & Automation System (MVP)

A modular, open-source MVP that automates task assignment and completion with AI agents, free/public APIs, and accessible tooling. Built around n8n workflows, an MCP server for tool calling, open‑source LLMs via Ollama, a Node/Express backend with MongoDB, an optional React UI, an NGINX reverse proxy, and a points‑based rewards system for users and drivers.

## Objectives
- Automate task assignment/completion using AI agents powered by open‑source tools
- Minimize cost using local models and free public APIs/URLs
- Orchestrate flows with n8n and tool calling via MCP
- Use Ollama for open‑source LLM inference
- Securely broker requests via an NGINX reverse proxy
- Implement a points‑based rewards system
- Ship a user‑friendly MVP with clear workflows and sample configs

## Architecture (High‑level)
- Frontend: React single‑page app (user + driver dashboards)
- Backend: Node.js + Express (REST APIs, webhooks for n8n)
- Database: MongoDB (users, tasks, rewards)
- Workflow: n8n (HTTP/webhook triggers → AI agent steps → actions)
- AI: Ollama local LLMs (e.g., Llama 3, Mistral)
- Tool Calling: MCP server (agents invoke tools like notify_driver)
- Proxy: NGINX reverse proxy (routes /api and selected external APIs)
- Rewards: Points for task completion and timeliness

Mermaid diagram (conceptual):

```mermaid
flowchart LR
  FE[React Frontend] -->|/api| BE[(Express API)]
  BE --> DB[(MongoDB)]
  BE <-- Webhook --> N8N[n8n Workflows]
  N8N <--> MCP[MCP Server]
  N8N <--> OLL[Ollama LLM]
  NGINX[NGINX Reverse Proxy] --> FE
  NGINX -->|/api| BE
  NGINX -->|/external| Ext[(Free Public APIs)]
```

## Quick Start (Local)
### Prerequisites
- Node.js 18+
- Docker (for n8n) and Git
- MongoDB running locally (e.g., mongod) or Atlas free tier
- Optional: NGINX installed locally for reverse proxy

### 1) Run n8n
```bash
docker run -it --rm --name n8n -p 5678:5678 n8n
```
- n8n UI: http://localhost:5678
- Create a workflow that starts on an HTTP Webhook or an HTTP Request node

### 2) Install Ollama and a model
```bash
# macOS/Linux
curl -fsSL https://ollama.ai/install.sh | sh
# Windows: https://ollama.ai/download

# Example models
ollama pull llama3.1:8b
# start server (default: http://localhost:11434)
ollama serve
```

### 3) MCP Server (tool calling)
- Use an open‑source MCP server or a lightweight Node service that exposes tools via HTTP for n8n to call.
- Example tools to expose:
  - notify_driver → POST http://localhost:8080/notify
  - complete_task → POST http://localhost:8080/tasks/:id/complete

### 4) Backend (Express)
Expose minimal REST endpoints:
- POST /api/tasks – create a task
- GET /api/tasks – list tasks
- PUT /api/tasks/:id – update status (e.g., pending → in_progress → completed)
- POST /api/webhooks/n8n – receive n8n callbacks

MongoDB schemas (conceptual):
- users: { id, name, points }
- tasks: { id, description, status, assignee }
- rewards: { userId, points, timestamp }

### 5) NGINX Reverse Proxy (optional, local)
Basic example:
```nginx
server {
  listen 80;
  location /api/ { proxy_pass http://localhost:3000; }
  location /external/ { proxy_pass http://api.openweathermap.org; }
}
```
Access backend via http://localhost/api (when NGINX is active).

## n8n Workflow Example (MVP)
- Trigger: HTTP Request or Webhook receives a new task payload
- Step 1: HTTP node → call Ollama with task description for classification/assignment
- Step 2: Function/Set node → map LLM output to assignee + ETA + price breakdown
- Step 3: HTTP node → call MCP tool notify_driver
- Step 4: HTTP node → update task status in backend (e.g., assigned)
- Step 5: Optional → on completion, call backend to award points

Minimal Ollama call (HTTP Request node):
- Method: POST
- URL: http://localhost:11434/api/chat
- Body (JSON): { model: "llama3.1:8b", messages: [{ role: "user", content: "Classify/assign task: ..." }] }
- Parse response JSON in a Function node to extract fields

## Rewards System (Points)
- Base: +10 points per completed task
- Bonus: +5 points for completion under target ETA
- Storage: Write to rewards collection and increment users.points

## Free Public APIs (examples)
- OpenStreetMap (Nominatim): https://nominatim.openstreetmap.org/search
- OpenWeatherMap: http://api.openweathermap.org/data/2.5/weather

Environment variables (samples):
```
OPENWEATHERMAP_API_KEY=SAMPLE_API_KEY
FIREBASE_API_KEY=SAMPLE_API_KEY
MONGO_URL=mongodb://localhost:27017/electroride
PORT=3000
```

## Sample URLs
- Backend: http://localhost:3000/api
- n8n: http://localhost:5678
- MCP: http://localhost:8080/mcp
- NGINX proxy route (when enabled): http://localhost/api/tasks

## Frontend (optional MVP)
- Two dashboards:
  - User: create task, track status, view rewards
  - Driver: see assigned tasks, update status, view rewards
- Connect via fetch to /api/* and poll or use WebSocket later

## Testing the MVP
- Create a task via POST /api/tasks
- Verify n8n receives trigger and calls Ollama → assignment
- Confirm backend status updates and rewards awarded on completion
- Check UI reflects changes (if using React demo)

## Deployment (Free‑tier friendly)
- Host backend on Render/Fly.io/Heroku free tier
- n8n on a small VM/container (or n8n.cloud trial)
- MongoDB Atlas free tier
- Serve React static build via the backend or a static host (Netlify/Vercel)
- Use HTTPS and environment variables for secrets

## Security Notes
- Do not commit API keys; use .env
- Validate all inputs; add rate‑limits on public routes
- Consider auth (JWT) before real users

## License & Disclaimer
MIT License. This MVP is for educational/demo purposes. Use at your own risk.

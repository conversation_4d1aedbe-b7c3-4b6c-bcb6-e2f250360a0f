# AI Trading Automation Platform

A comprehensive trading automation platform powered by AI agents and workflows. This application provides real-time market data integration, automated trading strategies, risk management, and AI-driven decision making.

## 🚀 Features

### Core Trading Features
- **Real-time Market Data**: Integration with multiple free data sources (Yahoo Finance, Alpha Vantage, Finnhub, IEX Cloud)
- **Live Trading Simulation**: Paper trading with real market prices
- **Risk Management**: Automated position sizing and risk controls
- **Signal Generation**: AI-powered trading signals and technical analysis

### AI Workflow System
- **AI Agent Hub**: Deploy and manage multiple AI agents for different trading tasks
- **Workflow Automation**: Create custom workflows combining multiple AI agents
- **Free AI Providers**: Integration with open-source and free AI services
- **Real-time Monitoring**: Track all AI agents and workflows in real-time

## 🔧 AI Providers Supported

### Free & Open Source Options
1. **Groq** - Fast LLM inference with free tier
   - API: `https://api.groq.com/openai/v1`
   - Models: Llama-3.1-70B, Mixtral-8x7B
   - Free: 6,000 tokens/minute

2. **Hugging Face** - Transformers and models
   - API: `https://api-inference.huggingface.co/models/`
   - Free inference for thousands of models
   - Rate limits apply

3. **OpenRouter** - Multi-model API gateway
   - API: `https://openrouter.ai/api/v1`
   - Free models available
   - Pay-per-use pricing

4. **Ollama (Local)** - Run models locally
   - API: `http://localhost:11434/v1`
   - Completely free and private
   - No rate limits

5. **Together AI** - Collaborative AI platform
   - API: `https://api.together.xyz/v1`
   - Free credits for new users

## 🤖 Pre-built AI Agents

### 1. Market Sentiment Analyzer
- Analyzes news, social media, and price action
- Generates sentiment scores (-1 to 1)
- Provides buy/sell/hold recommendations

### 2. Risk Management Agent
- Monitors portfolio exposure and risk metrics
- Calculates position sizes based on risk parameters
- Automatically adjusts positions when thresholds are breached

### 3. Technical Analysis Agent
- Chart pattern recognition
- Technical indicator analysis (RSI, MACD, Bollinger Bands)
- Multi-timeframe analysis
- Entry/exit point recommendations

## 📊 Workflow Templates

### Complete Market Analysis
1. Data Collection → Sentiment Analysis → Technical Analysis → Risk Assessment → Trading Decision

### Automated Risk Monitoring
1. Portfolio Scan → Risk Calculation → Threshold Check → Auto-adjust Positions

### News-driven Trading
1. News Collection → Sentiment Analysis → Impact Assessment → Position Adjustment

## 🛠️ Getting Started

### Prerequisites
- Node.js 18+ and npm/yarn
- API keys for desired AI providers (optional - free tiers available)
- Market data API keys (optional - Yahoo Finance works without keys)

### Installation
```bash
# Clone the repository
git clone <repository-url>
cd ai-trading-platform

# Install dependencies
npm install

# Start development server
npm run dev
```

### Configuration

#### 1. Market Data Setup
Navigate to the Market Data Setup tab to configure:
- **Alpha Vantage**: Free 500 calls/day - [Get API Key](https://www.alphavantage.co/support/#api-key)
- **Finnhub**: Free 60 calls/minute - [Get API Key](https://finnhub.io/register)
- **IEX Cloud**: Free 50k credits/month - [Get API Key](https://iexcloud.io/cloud-login#/register)

#### 2. AI Provider Setup
In the AI Workflow Hub → AI Providers tab:

**Groq (Recommended)**
```bash
# Get free API key from https://console.groq.com/
API_KEY: your_groq_api_key
URL: https://api.groq.com/openai/v1
MODEL: llama3-70b-8192
```

**Local Ollama Setup**
```bash
# Install Ollama locally
curl -fsSL https://ollama.ai/install.sh | sh

# Pull a model
ollama pull llama3.1:8b

# Start server (runs on localhost:11434)
ollama serve
```

**Hugging Face**
```bash
# Get free API key from https://huggingface.co/settings/tokens
API_KEY: your_hf_token
URL: https://api-inference.huggingface.co/models/
MODEL: microsoft/DialoGPT-large
```

## 📈 Sample Workflow Implementation

### Example: Automated Trading Bot

```javascript
// 1. Create Market Sentiment Agent
const sentimentAgent = {
  provider: 'groq',
  model: 'llama3-70b-8192',
  systemPrompt: 'Analyze market sentiment and generate trading signals...',
  triggers: ['news_update', 'price_change'],
  actions: ['generate_signal', 'update_dashboard']
};

// 2. Create Risk Management Agent
const riskAgent = {
  provider: 'ollama',
  model: 'llama3.1:8b',
  systemPrompt: 'Monitor portfolio risk and adjust positions...',
  triggers: ['portfolio_update', 'volatility_spike'],
  actions: ['adjust_position', 'send_alert']
};

// 3. Create Workflow
const tradingWorkflow = {
  steps: [
    { type: 'data-collection', sources: ['yahoo_finance', 'news_api'] },
    { type: 'analysis', agent: 'sentiment-agent' },
    { type: 'analysis', agent: 'risk-agent' },
    { type: 'decision', strategy: 'combined_signals' },
    { type: 'action', execute: 'place_trade' }
  ],
  schedule: '*/5 * * * *' // Every 5 minutes
};
```

## 🔐 Security Features

- **Encrypted Storage**: All API keys are encrypted locally
- **Rate Limiting**: Built-in rate limiting for API calls
- **Input Validation**: Zod schemas for data validation
- **Secure Key Management**: No API keys stored in code

## 🎯 Use Cases

### For Individual Traders
- Automate technical analysis
- Get AI-powered market insights
- Manage portfolio risk automatically
- Paper trade strategies before going live

### For Algorithm Developers
- Test AI models on real market data
- Create custom trading workflows
- Backtest strategies with AI signals
- Monitor model performance

### For Educational Purposes
- Learn about AI in finance
- Understand market dynamics
- Practice risk management
- Experiment with different AI models

## 🚦 Testing the Platform

### Sample Test Configuration

1. **Market Data Test**
   - Symbol: `RELIANCE.NS` (Reliance Industries)
   - Data Source: Yahoo Finance (free)
   - Expected: Real-time price updates

2. **AI Agent Test**
   - Provider: Groq (free tier)
   - Model: `llama3-70b-8192`
   - Test Input: Market sentiment analysis request
   - Expected: JSON response with sentiment score

3. **Workflow Test**
   - Template: "Complete Market Analysis"
   - Symbols: `['RELIANCE', 'TCS', 'HDFCBANK']`
   - Expected: Full analysis report with recommendations

## 📱 UI/UX Features

### Modern Design System
- **Dark Theme**: Professional trading interface
- **Responsive Design**: Works on all device sizes
- **Real-time Updates**: Live data streaming
- **Interactive Charts**: Technical analysis visualization

### Enhanced Components
- **AI Agent Cards**: Visual status and configuration
- **Workflow Builder**: Drag-and-drop workflow creation
- **Real-time Monitoring**: Live agent activity feed
- **Alert System**: Toast notifications for important events

## 🔗 Integration Opportunities

### Webhook Integrations
- Discord/Slack notifications
- Email alerts via Zapier
- Custom webhook endpoints

### Data Sources
- Social media sentiment (Twitter, Reddit)
- Economic calendars
- Earnings announcements
- SEC filings

### Advanced AI Features
- Multi-modal analysis (text + charts)
- Reinforcement learning for strategy optimization
- Ensemble model predictions
- Backtesting with historical data

## 📚 Documentation Links

- [Groq API Documentation](https://console.groq.com/docs/quickstart)
- [Hugging Face API Guide](https://huggingface.co/docs/api-inference/quicktour)
- [Ollama Documentation](https://ollama.ai/library)
- [Alpha Vantage API](https://www.alphavantage.co/documentation/)
- [Yahoo Finance API Guide](https://github.com/ranaroussi/yfinance)

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## ⚠️ Disclaimer

This software is for educational and research purposes only. Always do your own research and consider the risks before making any trading decisions. The developers are not responsible for any financial losses incurred through the use of this software.
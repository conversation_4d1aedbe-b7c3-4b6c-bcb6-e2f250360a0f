// Minimal Express backend for ElectroRide MVP
// Note: Requires express and cors to be installed in this folder.
// Setup instructions: see ../README.md

const express = require('express');
const fetch = require('node-fetch');

const cors = require('cors');

const apiKey = require('./middleware/apiKey');

const app = express();
const PORT = process.env.PORT || 3000;
const N8N_WEBHOOK_URL = process.env.N8N_WEBHOOK_URL || 'http://localhost:5678/webhook/electroride/task';


// Basic CORS (dev-friendly)
app.use(cors());
app.use(express.json());

// In-memory stores for MVP
const users = new Map([
  ['driver1', { id: 'driver1', name: '<PERSON><PERSON>', points: 0 }],
  ['driver2', { id: 'driver2', name: '<PERSON><PERSON>', points: 0 }],
  ['driver3', { id: 'driver3', name: '<PERSON><PERSON>', points: 0 }],
]);

const tasks = new Map(); // id -> task

function genId() {
  return Date.now().toString(36) + Math.random().toString(36).slice(2, 8);
}

// Healthcheck
app.get('/api/health', (req, res) => {
  res.json({ ok: true, service: 'electroride-backend', time: new Date().toISOString() });
});

// List users (for demo/rewards)
app.get('/api/users', (req, res) => {
  res.json({ users: Array.from(users.values()) });
});

// Create task
app.post('/api/tasks', (req, res) => {
  const { description, pickup, dropoff, metadata } = req.body || {};
  const id = genId();
  const task = {
    id,
    description: description || 'New task',
    pickup: pickup || null,
    dropoff: dropoff || null,
    status: 'pending',
    assignee: null,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    metadata: metadata || {},
  };
  tasks.set(id, task);
  console.log('[task:create]', id, task);
  res.status(201).json({ task });
});

// Optional: Dedicated lead intake endpoint to forward to tasks
app.post('/api/leads', (req, res) => {
  const { name, email, phone, source, notes } = req.body || {};
  if (!name && !email) {
    return res.status(400).json({ error: 'Missing lead name or email' });
  }
  const description = `Lead: ${name || 'Unknown'} ${email ? '<' + email + '>' : ''}`.trim();
  const metadata = { type: 'lead', name, email, phone, source, notes, timestamp: new Date().toISOString() };
  const id = genId();
  const task = {
    id,
    description,
    pickup: null,
    dropoff: null,
    status: 'pending',
    assignee: null,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    metadata,
  };
  tasks.set(id, task);
  console.log('[lead:create->task]', id, task);
  res.status(201).json({ task });
});


// List tasks
app.get('/api/tasks', (req, res) => {
  res.json({ tasks: Array.from(tasks.values()) });
});

// Get single task
app.get('/api/tasks/:id', (req, res) => {
  const task = tasks.get(req.params.id);
  if (!task) return res.status(404).json({ error: 'Task not found' });
  res.json({ task });
});

// Update task
app.put('/api/tasks/:id', (req, res) => {
  const task = tasks.get(req.params.id);
  if (!task) return res.status(404).json({ error: 'Task not found' });

  const { status, assignee, description, pickup, dropoff, metadata } = req.body || {};
  if (status) task.status = status;
  if (assignee !== undefined) task.assignee = assignee;
  if (description !== undefined) task.description = description;
  if (pickup !== undefined) task.pickup = pickup;
  if (dropoff !== undefined) task.dropoff = dropoff;
  if (metadata !== undefined) task.metadata = metadata;
  task.updatedAt = new Date().toISOString();

  tasks.set(task.id, task);
  console.log('[task:update]', task.id, task);
  res.json({ task });
});

// Complete task and award points
app.post('/api/tasks/:id/complete', (req, res) => {
  const task = tasks.get(req.params.id);
  if (!task) return res.status(404).json({ error: 'Task not found' });

  task.status = 'completed';
  task.updatedAt = new Date().toISOString();

  // Award points to assignee if exists
  const driverId = task.assignee;
  const basePoints = 10;
  const bonus = req.body && req.body.bonus ? Number(req.body.bonus) : 0;

  if (driverId && users.has(driverId)) {
    const user = users.get(driverId);
    user.points += basePoints + bonus;
    users.set(driverId, user);
    console.log('[rewards]', driverId, `+${basePoints + bonus}`);
  }

  tasks.set(task.id, task);
  res.json({ task, awarded: basePoints + bonus });
});

// n8n webhook (example)
app.post('/api/webhooks/n8n', (req, res) => {
  console.log('[webhook:n8n]', req.body);
  res.json({ ok: true });
});

// MCP notify mock (optional)
app.post('/api/notify', apiKey, (req, res) => {
  const { driverId, message } = req.body || {};
  console.log('[notify]', driverId, message);
  res.json({ ok: true });
});

// Trigger n8n workflow for a task (backend proxy)
app.post('/api/tasks/:id/trigger', apiKey, async (req, res) => {
  const task = tasks.get(req.params.id);
  if (!task) return res.status(404).json({ error: 'Task not found' });
  try {
    const body = {
      taskId: task.id,
      description: task.description,
      pickup: task.pickup,
      dropoff: task.dropoff,
    };
    const r = await fetch(N8N_WEBHOOK_URL, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(body),
    });
    const data = await r.json().catch(() => ({}));
    res.json({ ok: true, n8n: data });
  } catch (e) {
    console.error('n8n trigger failed', e);
    res.status(500).json({ error: 'Failed to trigger n8n' });
  }
});

if (require.main === module) {
  app.listen(PORT, () => console.log(`ElectroRide backend listening on http://localhost:${PORT}`));
}

module.exports = app;


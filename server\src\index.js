// Minimal Express backend for ElectroRide MVP
// Note: Requires express and cors to be installed in this folder.
// Setup instructions: see ../README.md

const express = require('express');
const cors = require('cors');

const app = express();
const PORT = process.env.PORT || 3000;

// Basic CORS (dev-friendly)
app.use(cors());
app.use(express.json());

// In-memory stores for MVP
const users = new Map([
  ['driver1', { id: 'driver1', name: '<PERSON><PERSON>', points: 0 }],
  ['driver2', { id: 'driver2', name: '<PERSON>riya M.', points: 0 }],
  ['driver3', { id: 'driver3', name: 'Amit S.', points: 0 }],
]);

const tasks = new Map(); // id -> task

function genId() {
  return Date.now().toString(36) + Math.random().toString(36).slice(2, 8);
}

// Healthcheck
app.get('/api/health', (req, res) => {
  res.json({ ok: true, service: 'electroride-backend', time: new Date().toISOString() });
});

// List users (for demo/rewards)
app.get('/api/users', (req, res) => {
  res.json({ users: Array.from(users.values()) });
});

// Create task
app.post('/api/tasks', (req, res) => {
  const { description, pickup, dropoff, metadata } = req.body || {};
  const id = genId();
  const task = {
    id,
    description: description || 'New task',
    pickup: pickup || null,
    dropoff: dropoff || null,
    status: 'pending',
    assignee: null,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    metadata: metadata || {},
  };
  tasks.set(id, task);
  console.log('[task:create]', id, task);
  res.status(201).json({ task });
});

// List tasks
app.get('/api/tasks', (req, res) => {
  res.json({ tasks: Array.from(tasks.values()) });
});

// Get single task
app.get('/api/tasks/:id', (req, res) => {
  const task = tasks.get(req.params.id);
  if (!task) return res.status(404).json({ error: 'Task not found' });
  res.json({ task });
});

// Update task
app.put('/api/tasks/:id', (req, res) => {
  const task = tasks.get(req.params.id);
  if (!task) return res.status(404).json({ error: 'Task not found' });

  const { status, assignee, description, pickup, dropoff, metadata } = req.body || {};
  if (status) task.status = status;
  if (assignee !== undefined) task.assignee = assignee;
  if (description !== undefined) task.description = description;
  if (pickup !== undefined) task.pickup = pickup;
  if (dropoff !== undefined) task.dropoff = dropoff;
  if (metadata !== undefined) task.metadata = metadata;
  task.updatedAt = new Date().toISOString();

  tasks.set(task.id, task);
  console.log('[task:update]', task.id, task);
  res.json({ task });
});

// Complete task and award points
app.post('/api/tasks/:id/complete', (req, res) => {
  const task = tasks.get(req.params.id);
  if (!task) return res.status(404).json({ error: 'Task not found' });

  task.status = 'completed';
  task.updatedAt = new Date().toISOString();

  // Award points to assignee if exists
  const driverId = task.assignee;
  const basePoints = 10;
  const bonus = req.body && req.body.bonus ? Number(req.body.bonus) : 0;

  if (driverId && users.has(driverId)) {
    const user = users.get(driverId);
    user.points += basePoints + bonus;
    users.set(driverId, user);
    console.log('[rewards]', driverId, `+${basePoints + bonus}`);
  }

  tasks.set(task.id, task);
  res.json({ task, awarded: basePoints + bonus });
});

// n8n webhook (example)
app.post('/api/webhooks/n8n', (req, res) => {
  console.log('[webhook:n8n]', req.body);
  res.json({ ok: true });
});

// MCP notify mock (optional)
app.post('/api/notify', (req, res) => {
  const { driverId, message } = req.body || {};
  console.log('[notify]', driverId, message);
  res.json({ ok: true });
});

if (require.main === module) {
  app.listen(PORT, () => console.log(`ElectroRide backend listening on http://localhost:${PORT}`));
}

module.exports = app;


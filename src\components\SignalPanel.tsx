
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Brain, 
  TrendingUp, 
  TrendingDown, 
  Activity, 
  Target,
  Clock,
  BarChart3,
  Zap
} from 'lucide-react';
import { useSignalGeneration } from '@/hooks/useSignalGeneration';
import { MarketSignal } from '@/types/signals';

const SignalPanel = () => {
  const { 
    signals, 
    isGenerating, 
    lastUpdate,
    generateSignalsForSymbol,
    getSignalsByType 
  } = useSignalGeneration();
  
  const [selectedSignal, setSelectedSignal] = useState<MarketSignal | null>(null);

  const getSignalIcon = (signal: 'BUY' | 'SELL' | 'HOLD') => {
    switch (signal) {
      case 'BUY':
        return <TrendingUp className="w-4 h-4 text-green-400" />;
      case 'SELL':
        return <TrendingDown className="w-4 h-4 text-red-400" />;
      default:
        return <Activity className="w-4 h-4 text-yellow-400" />;
    }
  };

  const getSignalColor = (signal: 'BUY' | 'SELL' | 'HOLD') => {
    switch (signal) {
      case 'BUY':
        return 'bg-green-900/20 text-green-400 border-green-500';
      case 'SELL':
        return 'bg-red-900/20 text-red-400 border-red-500';
      default:
        return 'bg-yellow-900/20 text-yellow-400 border-yellow-500';
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 80) return 'text-green-400';
    if (confidence >= 60) return 'text-yellow-400';
    return 'text-red-400';
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-IN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const technicalSignals = getSignalsByType('TECHNICAL');
  const activeSignals = signals.filter(s => !s.expiresAt || s.expiresAt.getTime() > Date.now());

  return (
    <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Brain className="w-5 h-5 text-purple-400" />
            Signal Generation Engine
            {isGenerating && (
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
                <span className="text-xs text-purple-400">Generating...</span>
              </div>
            )}
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="bg-blue-900/20 text-blue-400 border-blue-500">
              {activeSignals.length} Active
            </Badge>
            <Button 
              size="sm" 
              onClick={() => generateSignalsForSymbol('NIFTY50')}
              disabled={isGenerating}
              className="bg-purple-600 hover:bg-purple-700"
            >
              <Zap className="w-3 h-3 mr-1" />
              Refresh
            </Button>
          </div>
        </CardTitle>
        {lastUpdate && (
          <p className="text-xs text-slate-400">
            Last updated: {formatTime(lastUpdate)}
          </p>
        )}
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="active" className="space-y-4">
          <TabsList className="grid w-full grid-cols-3 bg-slate-700/50">
            <TabsTrigger value="active">Active Signals</TabsTrigger>
            <TabsTrigger value="technical">Technical</TabsTrigger>
            <TabsTrigger value="performance">Performance</TabsTrigger>
          </TabsList>

          <TabsContent value="active" className="space-y-3">
            {activeSignals.length === 0 ? (
              <div className="text-center py-8 text-slate-400">
                <Activity className="w-8 h-8 mx-auto mb-2 opacity-50" />
                <p>No active signals</p>
                <p className="text-xs">Signals will appear automatically</p>
              </div>
            ) : (
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {activeSignals
                  .sort((a, b) => b.confidence - a.confidence)
                  .map((signal) => (
                    <div
                      key={signal.id}
                      className="p-3 bg-slate-700/30 rounded-lg border border-slate-600 hover:bg-slate-700/50 cursor-pointer transition-colors"
                      onClick={() => setSelectedSignal(signal)}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-white">{signal.symbol}</span>
                          <Badge className={getSignalColor(signal.signal)}>
                            {getSignalIcon(signal.signal)}
                            {signal.signal}
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            {signal.timeframe}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className={`text-sm font-medium ${getConfidenceColor(signal.confidence)}`}>
                            {signal.confidence.toFixed(0)}%
                          </div>
                          <Clock className="w-3 h-3 text-slate-400" />
                          <span className="text-xs text-slate-400">
                            {formatTime(signal.timestamp)}
                          </span>
                        </div>
                      </div>

                      <div className="flex items-center justify-between text-sm">
                        <div className="text-slate-300">
                          ₹{signal.price.toLocaleString()}
                        </div>
                        <div className="flex items-center gap-3 text-xs text-slate-400">
                          {signal.targetPrice && (
                            <div className="flex items-center gap-1">
                              <Target className="w-3 h-3" />
                              ₹{signal.targetPrice.toLocaleString()}
                            </div>
                          )}
                          {signal.stopLoss && (
                            <div className="flex items-center gap-1">
                              <Activity className="w-3 h-3" />
                              ₹{signal.stopLoss.toLocaleString()}
                            </div>
                          )}
                        </div>
                      </div>

                      <p className="text-xs text-slate-400 mt-2 line-clamp-2">
                        {signal.reasoning}
                      </p>

                      {/* Technical Indicators Summary */}
                      <div className="flex items-center gap-1 mt-2">
                        {signal.indicators.slice(0, 3).map((indicator, idx) => (
                          <Badge
                            key={idx}
                            variant="outline"
                            className="text-xs px-2 py-0 text-slate-400 border-slate-600"
                          >
                            {indicator.name}
                          </Badge>
                        ))}
                        {signal.indicators.length > 3 && (
                          <span className="text-xs text-slate-500">
                            +{signal.indicators.length - 3} more
                          </span>
                        )}
                      </div>
                    </div>
                  ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="technical" className="space-y-3">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {technicalSignals.slice(0, 6).map((signal) => (
                <div
                  key={signal.id}
                  className="p-3 bg-slate-700/30 rounded-lg border border-slate-600"
                >
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium text-white">{signal.symbol}</span>
                    <Badge className={getSignalColor(signal.signal)}>
                      {signal.signal}
                    </Badge>
                  </div>
                  
                  <div className="space-y-2">
                    {signal.indicators.map((indicator, idx) => (
                      <div key={idx} className="flex items-center justify-between text-sm">
                        <span className="text-slate-400">{indicator.name}:</span>
                        <div className="flex items-center gap-2">
                          <span className="text-white">{indicator.value.toFixed(2)}</span>
                          <Badge
                            variant="outline"
                            className={`text-xs ${getSignalColor(indicator.signal)}`}
                          >
                            {indicator.signal}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="performance" className="space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-400">
                  {signals.filter(s => s.signal === 'BUY').length}
                </div>
                <div className="text-xs text-slate-400">Buy Signals</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-400">
                  {signals.filter(s => s.signal === 'SELL').length}
                </div>
                <div className="text-xs text-slate-400">Sell Signals</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-400">
                  {signals.length > 0 ? (signals.reduce((sum, s) => sum + s.confidence, 0) / signals.length).toFixed(0) : 0}%
                </div>
                <div className="text-xs text-slate-400">Avg Confidence</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-400">
                  {signals.filter(s => s.confidence >= 80).length}
                </div>
                <div className="text-xs text-slate-400">High Confidence</div>
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="text-sm font-medium text-slate-300">Signal Distribution</h4>
              <div className="grid grid-cols-3 gap-2">
                <div className="flex items-center gap-2 text-sm">
                  <div className="w-3 h-3 bg-green-500 rounded"></div>
                  <span className="text-slate-400">Buy: {signals.filter(s => s.signal === 'BUY').length}</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <div className="w-3 h-3 bg-red-500 rounded"></div>
                  <span className="text-slate-400">Sell: {signals.filter(s => s.signal === 'SELL').length}</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <div className="w-3 h-3 bg-yellow-500 rounded"></div>
                  <span className="text-slate-400">Hold: {signals.filter(s => s.signal === 'HOLD').length}</span>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default SignalPanel;

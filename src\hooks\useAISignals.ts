
import { useState, useEffect, useCallback } from 'react';
import { MarketSignal } from '@/types/signals';
import { NewsArticle, SocialMediaPost, AISignalConfig } from '@/types/ai';
import { AISignalGenerator } from '@/services/AISignalGenerator';
import { useSignalGeneration } from './useSignalGeneration';

const defaultAIConfig: AISignalConfig = {
  sentimentWeight: 0.3,
  newsWeight: 0.4,
  socialWeight: 0.3,
  mlWeight: 0.4,
  minSentimentScore: 0.1,
  lookbackPeriod: 24
};

// Mock data generators
const generateMockNews = (symbol: string): NewsArticle[] => {
  const headlines = [
    `${symbol} reports strong quarterly results`,
    `Analysts upgrade ${symbol} target price`,
    `${symbol} announces new partnership deal`,
    `Market volatility affects ${symbol} trading`,
    `${symbol} shows resilient performance`
  ];
  
  return headlines.map((title, index) => ({
    id: `news_${symbol}_${index}`,
    title,
    content: `${title}. Detailed analysis shows positive market sentiment and strong fundamentals.`,
    source: `Financial Times ${index + 1}`,
    url: `https://example.com/news/${index}`,
    timestamp: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000),
    symbols: [symbol],
    sentiment: {
      polarity: (Math.random() - 0.5) * 2,
      confidence: Math.random() * 0.5 + 0.5,
      label: Math.random() > 0.6 ? 'POSITIVE' : Math.random() > 0.3 ? 'NEGATIVE' : 'NEUTRAL',
      subjectivity: Math.random() * 0.8 + 0.2
    },
    impact: Math.random() > 0.7 ? 'HIGH' : Math.random() > 0.4 ? 'MEDIUM' : 'LOW'
  }));
};

const generateMockSocialPosts = (symbol: string): SocialMediaPost[] => {
  const posts = [
    `${symbol} looking bullish! 🚀 #investing`,
    `Just bought more ${symbol} shares, great value here`,
    `${symbol} chart pattern looks promising`,
    `Concerns about ${symbol} recent performance`,
    `${symbol} fundamentals remain strong`
  ];
  
  return posts.map((content, index) => ({
    id: `social_${symbol}_${index}`,
    platform: ['TWITTER', 'REDDIT', 'DISCORD'][Math.floor(Math.random() * 3)] as any,
    content,
    author: `user_${index + 1}`,
    timestamp: new Date(Date.now() - Math.random() * 12 * 60 * 60 * 1000),
    engagement: {
      likes: Math.floor(Math.random() * 100) + 10,
      shares: Math.floor(Math.random() * 50) + 1,
      comments: Math.floor(Math.random() * 30) + 1
    },
    sentiment: {
      polarity: (Math.random() - 0.5) * 2,
      confidence: Math.random() * 0.5 + 0.5,
      label: Math.random() > 0.6 ? 'POSITIVE' : Math.random() > 0.3 ? 'NEGATIVE' : 'NEUTRAL',
      subjectivity: Math.random() * 0.8 + 0.2
    },
    symbols: [symbol]
  }));
};

export const useAISignals = () => {
  const { config: baseConfig } = useSignalGeneration();
  const [aiSignals, setAISignals] = useState<MarketSignal[]>([]);
  const [aiConfig, setAIConfig] = useState<AISignalConfig>(defaultAIConfig);
  const [aiGenerator, setAIGenerator] = useState<AISignalGenerator | null>(null);
  const [isGeneratingAI, setIsGeneratingAI] = useState(false);
  const [newsData, setNewsData] = useState<NewsArticle[]>([]);
  const [socialData, setSocialData] = useState<SocialMediaPost[]>([]);

  // Initialize AI generator
  useEffect(() => {
    const generator = new AISignalGenerator(baseConfig, aiConfig);
    setAIGenerator(generator);
  }, [baseConfig, aiConfig]);

  /**
   * Generate AI-enhanced signals for a symbol
   */
  const generateAISignalsForSymbol = useCallback(async (
    symbol: string,
    timeframe: '1m' | '5m' | '15m' | '1h' | '4h' | '1d' = '15m'
  ) => {
    if (!aiGenerator) return [];

    setIsGeneratingAI(true);
    try {
      // Generate mock data (in production, this would fetch real data)
      const mockOHLCV = Array.from({ length: 50 }, (_, i) => ({
        open: 15000 + Math.random() * 100,
        high: 15000 + Math.random() * 150,
        low: 15000 - Math.random() * 100,
        close: 15000 + Math.random() * 100,
        volume: Math.floor(Math.random() * 1000000) + 500000,
        timestamp: new Date(Date.now() - (50 - i) * 60000)
      }));

      const news = generateMockNews(symbol);
      const social = generateMockSocialPosts(symbol);
      
      setNewsData(prev => [...prev.filter(n => !n.symbols.includes(symbol)), ...news]);
      setSocialData(prev => [...prev.filter(s => !s.symbols.includes(symbol)), ...social]);

      const signals = await aiGenerator.generateAISignals(
        symbol,
        mockOHLCV,
        news,
        social,
        timeframe
      );

      setAISignals(prev => {
        const filtered = prev.filter(s => s.symbol !== symbol);
        return [...filtered, ...signals];
      });

      console.log(`Generated ${signals.length} AI signals for ${symbol}:`, signals);
      return signals;
    } catch (error) {
      console.error(`Error generating AI signals for ${symbol}:`, error);
      return [];
    } finally {
      setIsGeneratingAI(false);
    }
  }, [aiGenerator]);

  /**
   * Update AI configuration
   */
  const updateAIConfig = useCallback((newConfig: Partial<AISignalConfig>) => {
    setAIConfig(prev => ({ ...prev, ...newConfig }));
  }, []);

  /**
   * Get AI signals for a specific symbol
   */
  const getAISignalsForSymbol = useCallback((symbol: string) => {
    return aiSignals.filter(signal => signal.symbol === symbol);
  }, [aiSignals]);

  // Auto-generate AI signals for demo
  useEffect(() => {
    const symbols = ['NIFTY50', 'BANKNIFTY', 'RELIANCE'];
    
    const generateDemo = async () => {
      for (const symbol of symbols) {
        await generateAISignalsForSymbol(symbol);
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    };

    // Initial generation
    generateDemo();

    // Periodic updates every 5 minutes
    const interval = setInterval(generateDemo, 300000);
    
    return () => clearInterval(interval);
  }, [generateAISignalsForSymbol]);

  return {
    aiSignals,
    aiConfig,
    newsData,
    socialData,
    isGeneratingAI,
    generateAISignalsForSymbol,
    getAISignalsForSymbol,
    updateAIConfig
  };
};

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>ElectroRide AI-Powered Workflow System – UI Demo</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
  <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
  <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
  <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#2563eb',
            secondary: '#10b981',
            accent: '#f59e0b',
            dark: '#1f2937',
            light: '#f9fafb'
          }
        }
      }
    };
  </script>
  <style>
    body { background-color: white; }
    .ai-card { box-shadow: 0 10px 25px -5px rgba(0,0,0,0.1), 0 10px 10px -5px rgba(0,0,0,0.04); transition: all .3s ease; }
    .ai-card:hover { transform: translateY(-5px); box-shadow: 0 20px 25px -5px rgba(0,0,0,0.1), 0 10px 10px -5px rgba(0,0,0,0.04); }
    .confidence-bar { height: 8px; border-radius: 4px; transition: width .5s ease; }
    .fade-in { animation: fadeIn .5s ease-in; }
    @keyframes fadeIn { from { opacity: 0; transform: translateY(10px); } to { opacity: 1; transform: translateY(0); } }
    .pulse { animation: pulse 2s infinite; }
    @keyframes pulse { 0% { box-shadow: 0 0 0 0 rgba(37,99,235,.4); } 70% { box-shadow: 0 0 0 10px rgba(37,99,235,0); } 100% { box-shadow: 0 0 0 0 rgba(37,99,235,0); } }
  </style>
</head>
<body class="bg-gray-50 min-h-screen">
  <div id="root"></div>

  <script type="text/babel">
    const { useState, useRef } = React;

    // Mock AI Service Output (from your provided example)
    const mockAIRecommendations = {
      bestVehicleType: 'three-wheeler',
      estimatedPrice: 150,
      priceBreakdown: {
        baseFare: 100,
        distance: 30,
        time: 15,
        dynamicPricing: 5,
        serviceFee: 10,
      },
      estimatedTime: 7,
      recommendedDrivers: [
        { id: 'driver1', name: 'Rajesh K.', rating: 4.8, distance: 1.2, eta: 5, reason: 'Closest to pickup location with highest rating' },
        { id: 'driver2', name: 'Priya M.', rating: 4.7, distance: 2.1, eta: 8, reason: 'Has experience with electric vehicles' },
        { id: 'driver3', name: 'Amit S.',  rating: 4.9, distance: 3.5, eta: 10, reason: 'Most reliable driver in area' },
      ],
      confidenceLevel: 0.85,
      confidenceFactors: {
        traffic: 0.8,
        weather: 0.9,
        demand: 0.75,
        historicalAccuracy: 0.85,
      },
    };

    const AIRecommendationCard = ({ recommendation, isLoading, error, onRetry }) => {
      const [showConfidence, setShowConfidence] = useState(false);

      if (isLoading) {
        return (
          <div className="bg-white rounded-xl ai-card p-4 mb-4 fade-in">
            <div className="animate-pulse">
              <div className="h-6 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
              <div className="h-10 bg-gray-200 rounded mb-3"></div>
              <div className="h-4 bg-gray-200 rounded w-2/3 mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-1/3"></div>
            </div>
          </div>
        );
      }

      if (error) {
        return (
          <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-4 mb-4 fade-in">
            <div className="flex items-center">
              <i className="fas fa-exclamation-triangle text-yellow-500 text-xl mr-3"></i>
              <div>
                <p className="font-medium text-yellow-800">AI recommendations temporarily unavailable</p>
                <p className="text-sm text-yellow-700 mt-1">Using standard pricing and vehicle selection. We're working to restore AI features.</p>
              </div>
            </div>
            <button onClick={onRetry} className="mt-3 text-sm font-medium text-yellow-700 hover:text-yellow-800 flex items-center">
              <i className="fas fa-redo mr-1"></i> Try again
            </button>
          </div>
        );
      }

      const getConfidenceColor = (level) => (level >= 0.8 ? 'bg-green-500' : level >= 0.6 ? 'bg-yellow-500' : 'bg-red-500');
      const getConfidenceLabel = (level) => (level >= 0.8 ? 'High Confidence' : level >= 0.6 ? 'Medium Confidence' : 'Low Confidence');

      return (
        <div className="bg-white rounded-xl ai-card p-4 mb-4 fade-in">
          <div className="flex items-center mb-3">
            <div className="p-2 bg-blue-100 rounded-full mr-3">
              <i className="fas fa-robot text-blue-600 text-xl"></i>
            </div>
            <div>
              <h3 className="font-bold text-lg text-gray-800">AI-Powered Recommendation</h3>
              <p className="text-sm text-gray-600">Based on your route and current conditions</p>
            </div>
          </div>

          <div className="bg-blue-50 rounded-lg p-3 mb-4">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-full mr-3">
                <i className="fas fa-car text-blue-600 text-xl"></i>
              </div>
              <div>
                <h4 className="font-semibold capitalize">{recommendation.bestVehicleType}</h4>
                <p className="text-sm text-gray-600">Best option for your trip based on traffic and weather conditions</p>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4 mb-4">
            <div className="bg-gray-50 rounded-lg p-3">
              <p className="text-sm text-gray-600">Estimated Price</p>
              <p className="text-2xl font-bold text-primary">₹{recommendation.estimatedPrice}</p>
            </div>
            <div className="bg-gray-50 rounded-lg p-3">
              <p className="text-sm text-gray-600">Estimated Time</p>
              <p className="text-xl font-medium text-secondary">{recommendation.estimatedTime} min</p>
            </div>
          </div>

          <div className="mb-4">
            <h4 className="text-sm font-semibold text-gray-700 mb-2">Price Breakdown</h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between"><span className="text-gray-600">Base Fare</span><span className="font-medium">₹{recommendation.priceBreakdown.baseFare}</span></div>
              <div className="flex justify-between"><span className="text-gray-600">Distance</span><span className="font-medium">₹{recommendation.priceBreakdown.distance}</span></div>
              <div className="flex justify-between"><span className="text-gray-600">Time</span><span className="font-medium">₹{recommendation.priceBreakdown.time}</span></div>
              <div className="flex justify-between"><span className="text-gray-600">Dynamic Pricing</span><span className="font-medium">₹{recommendation.priceBreakdown.dynamicPricing}</span></div>
              <div className="flex justify-between pt-2 border-t"><span className="font-semibold">Total</span><span className="font-bold text-lg">₹{Object.values(recommendation.priceBreakdown).reduce((a,b)=>a+b,0)}</span></div>
            </div>
          </div>

          <div className="mb-4">
            <h4 className="text-sm font-semibold text-gray-700 mb-2">Recommended Drivers</h4>
            <div className="space-y-3">
              {recommendation.recommendedDrivers.slice(0, 2).map((driver, index) => (
                <div key={driver.id || index} className="flex items-center p-3 bg-gray-50 rounded-lg">
                  <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden mr-3">
                    <i className="fas fa-user text-gray-500"></i>
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <div className="font-medium text-gray-800">{driver.name}</div>
                      <div className="text-sm text-gray-600">{driver.eta} min</div>
                    </div>
                    <div className="text-xs text-gray-600">Rating: {driver.rating} • {driver.distance} km away</div>
                    <div className="text-xs text-gray-500 mt-1">{driver.reason}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="mb-2">
            <div className="flex items-center justify-between text-sm">
              <span className="font-medium text-gray-700">{getConfidenceLabel(recommendation.confidenceLevel)}</span>
              <span className="text-gray-600">{Math.round(recommendation.confidenceLevel * 100)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
              <div className={`h-2 rounded-full ${getConfidenceColor(recommendation.confidenceLevel)} confidence-bar`} style={{ width: `${recommendation.confidenceLevel * 100}%` }}></div>
            </div>
            <button onClick={() => setShowConfidence(!showConfidence)} className="mt-2 text-xs text-blue-600 hover:underline">
              {showConfidence ? 'Hide confidence factors' : 'Show confidence factors'}
            </button>
            {showConfidence && (
              <div className="grid grid-cols-2 gap-2 text-xs text-gray-700 mt-2">
                <div className="flex justify-between bg-gray-50 p-2 rounded"><span>Traffic</span><span>{Math.round(recommendation.confidenceFactors.traffic * 100)}%</span></div>
                <div className="flex justify-between bg-gray-50 p-2 rounded"><span>Weather</span><span>{Math.round(recommendation.confidenceFactors.weather * 100)}%</span></div>
                <div className="flex justify-between bg-gray-50 p-2 rounded"><span>Demand</span><span>{Math.round(recommendation.confidenceFactors.demand * 100)}%</span></div>
                <div className="flex justify-between bg-gray-50 p-2 rounded"><span>Historical</span><span>{Math.round(recommendation.confidenceFactors.historicalAccuracy * 100)}%</span></div>
              </div>
            )}
          </div>
        </div>
      );
    };

    const App = () => {
      const [isLoading, setIsLoading] = useState(false);
      const [error, setError] = useState(false);
      const [recommendation, setRecommendation] = useState(mockAIRecommendations);
      const [pickup, setPickup] = useState('');
      const [dropoff, setDropoff] = useState('');
      const createdTaskIdRef = useRef(null);

      async function createTask() {
        const res = await fetch('http://localhost:3000/api/tasks', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ description: `Ride from ${pickup} to ${dropoff}`, pickup, dropoff })
        });
        const data = await res.json();
        return data.task?.id;
      }

      async function triggerN8N(taskId) {
        const res = await fetch(`http://localhost:3000/api/tasks/${taskId}/trigger`, { method: 'POST' });
        return res.json();
      }

      const getRecommendation = async () => {
        try {
          setError(false);
          setIsLoading(true);
          const taskId = await createTask();
          createdTaskIdRef.current = taskId;
          await triggerN8N(taskId);
          // For demo, we keep using the mock recommendation until backend is wired to persist LLM outputs
          setRecommendation(mockAIRecommendations);
        } catch (e) {
          console.error(e);
          setError(true);
        } finally {
          setIsLoading(false);
        }
      };

      const onRetry = () => getRecommendation();

      return (
        <div className="max-w-3xl mx-auto p-4">
          <header className="mb-6">
            <div className="flex items-center">
              <div className="p-3 bg-primary text-white rounded-xl mr-3 shadow-md"><i className="fas fa-bolt"></i></div>
              <div>
                <h1 className="text-2xl font-bold text-gray-800">ElectroRide – AI-Powered Workflow System</h1>
                <p className="text-sm text-gray-600">Demo UI: AI recommendations, pricing, driver suggestions, and confidence factors</p>
              </div>
            </div>
          </header>

          <section className="bg-white rounded-xl ai-card p-4 mb-4">
            <h2 className="font-semibold text-gray-800 mb-3">Trip Details</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <input value={pickup} onChange={(e)=>setPickup(e.target.value)} className="border rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-primary" placeholder="Pickup Location" />
              <input value={dropoff} onChange={(e)=>setDropoff(e.target.value)} className="border rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-primary" placeholder="Drop Location" />
            </div>
            <div className="flex items-center mt-3">
              <button onClick={getRecommendation} className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-blue-600 transition flex items-center">
                <i className="fas fa-magic mr-2"></i>
                Get AI Recommendation
              </button>
              {isLoading && <span className="ml-3 text-sm text-gray-600">Analyzing…</span>}
            </div>
          </section>

          <AIRecommendationCard
            recommendation={recommendation}
            isLoading={isLoading}
            error={error}
            onRetry={onRetry}
          />

          <footer className="text-xs text-gray-500 mt-6">
            This is a static UI demo using React + Tailwind from CDN and mock data. Integrate with n8n/Ollama/MCP for live recommendations.
          </footer>
        </div>
      );
    };

    ReactDOM.createRoot(document.getElementById('root')).render(<App />);
  </script>
</body>
</html>


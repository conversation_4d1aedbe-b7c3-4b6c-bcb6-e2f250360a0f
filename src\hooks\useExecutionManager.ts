
import { useAutoTrading } from './useAutoTrading';
import { useRiskManagement } from './useRiskManagement';
import { useSignalGeneration } from './useSignalGeneration';
import { useBrokerConnection } from './useBrokerConnection';

export const useExecutionManager = () => {
  const { account, riskParams } = useRiskManagement();
  const { signals } = useSignalGeneration();
  const { broker, isConnected } = useBrokerConnection();

  const {
    executionConfig,
    executions,
    isAutoTrading,
    toggleAutoTrading,
    executeSignalManually,
    updateExecutionConfig,
    getExecutionStats
  } = useAutoTrading({
    broker,
    isConnected,
    account,
    riskParams,
    signals
  });

  return {
    executionConfig,
    executions,
    isAutoTrading,
    toggleAutoTrading,
    executeSignalManually,
    updateExecutionConfig,
    getExecutionStats
  };
};

export interface OHLCV {
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  timestamp: Date;
}

export interface TechnicalIndicator {
  name: string;
  value: number;
  signal: 'BUY' | 'SELL' | 'HOLD';
  strength: number; // 0-100
  timestamp: Date;
}

export interface MarketSignal {
  id: string;
  symbol: string;
  type: 'TECHNICAL' | 'SENTIMENT' | 'AI_PREDICTION' | 'FUNDAMENTAL';
  signal: 'BUY' | 'SELL' | 'HOLD';
  confidence: number; // 0-100
  strength: number; // 0-100
  price: number;
  targetPrice?: number;
  stopLoss?: number;
  timeframe: '1m' | '5m' | '15m' | '1h' | '4h' | '1d';
  indicators: TechnicalIndicator[];
  reasoning: string;
  timestamp: Date;
  expiresAt?: Date;
}

export interface SignalGenerationConfig {
  enabledIndicators: string[];
  minConfidence: number;
  maxSignalsPerSymbol: number;
  signalExpiryMinutes: number;
  riskRewardRatio: number;
}

export interface BacktestResult {
  signalId: string;
  entryPrice: number;
  exitPrice?: number;
  pnl?: number;
  duration?: number;
  accuracy: boolean;
  maxDrawdown: number;
}

export interface SignalPerformance {
  signalType: string;
  totalSignals: number;
  successRate: number;
  avgReturn: number;
  maxDrawdown: number;
  sharpeRatio?: number;
  lastUpdated: Date;
}

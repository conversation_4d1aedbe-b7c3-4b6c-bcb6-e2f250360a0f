
export interface NewsArticle {
  id: string;
  title: string;
  content: string;
  source: string;
  url: string;
  timestamp: Date;
  symbols: string[];
  sentiment: SentimentScore;
  impact: 'HIGH' | 'MEDIUM' | 'LOW';
}

export interface SentimentScore {
  polarity: number; // -1 to 1
  confidence: number; // 0 to 1
  label: 'POSITIVE' | 'NEGATIVE' | 'NEUTRAL';
  subjectivity: number; // 0 to 1
}

export interface SocialMediaPost {
  id: string;
  platform: 'TWITTER' | 'REDDIT' | 'DISCORD';
  content: string;
  author: string;
  timestamp: Date;
  engagement: {
    likes: number;
    shares: number;
    comments: number;
  };
  sentiment: SentimentScore;
  symbols: string[];
}

export interface MarketDataPoint {
  symbol: string;
  timestamp: Date;
  price: number;
  volume: number;
  volatility: number;
  marketCap?: number;
  sentiment: SentimentScore;
  newsCount: number;
  socialMentions: number;
}

export interface MLPrediction {
  symbol: string;
  timeframe: '1h' | '4h' | '1d';
  prediction: {
    direction: 'UP' | 'DOWN' | 'SIDEWAYS';
    confidence: number;
    targetPrice: number;
    probability: number;
  };
  features: {
    technical: number[];
    sentiment: number;
    volume: number;
    volatility: number;
  };
  model: string;
  timestamp: Date;
}

export interface AISignalConfig {
  sentimentWeight: number;
  newsWeight: number;
  socialWeight: number;
  mlWeight: number;
  minSentimentScore: number;
  lookbackPeriod: number;
}

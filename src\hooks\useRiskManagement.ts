
import { useState, useEffect } from 'react';
import { AccountInfo, RiskParameters, TradeRequest, RiskValidationResult } from '@/types/trading';
import { RiskManager } from '@/services/RiskManager';

const defaultRiskParams: RiskParameters = {
  maxRiskPerTrade: 2,
  maxPortfolioRisk: 10,
  maxSectorExposure: 10,
  maxSingleStockExposure: 5,
  dailyLossLimit: 10000,
  maxDrawdown: 15,
  minRiskRewardRatio: 2.0
};

const mockAccount: AccountInfo = {
  totalBalance: 250000,
  availableBalance: 48500,
  usedMargin: 201500,
  dailyPnL: 12500,
  totalPnL: 35000,
  positions: [
    {
      symbol: 'RELIANCE',
      quantity: 100,
      averagePrice: 2450,
      currentPrice: 2475,
      unrealizedPnL: 2500,
      sector: 'Energy',
      marketValue: 247500
    }
  ]
};

export const useRiskManagement = () => {
  const [account, setAccount] = useState<AccountInfo>(mockAccount);
  const [riskParams, setRiskParams] = useState<RiskParameters>(defaultRiskParams);
  const [riskManager, setRiskManager] = useState<RiskManager | null>(null);

  useEffect(() => {
    const manager = new RiskManager(account, riskParams);
    setRiskManager(manager);
  }, [account, riskParams]);

  const validateTrade = (trade: TradeRequest, sector?: string): RiskValidationResult | null => {
    if (!riskManager) return null;
    return riskManager.validateTrade(trade, sector);
  };

  const getRiskMetrics = () => {
    if (!riskManager) return null;
    return riskManager.getRiskMetrics();
  };

  const updateRiskParameters = (newParams: Partial<RiskParameters>) => {
    setRiskParams(prev => ({ ...prev, ...newParams }));
  };

  const updateAccount = (newAccount: Partial<AccountInfo>) => {
    setAccount(prev => ({ ...prev, ...newAccount }));
  };

  return {
    account,
    riskParams,
    validateTrade,
    getRiskMetrics,
    updateRiskParameters,
    updateAccount,
    riskManager
  };
};

import { MarketSignal } from '@/types/signals';
import { TradeExecution, OrderRequest } from '@/types/broker';
import { BrokerInterface } from './BrokerInterface';
import { RiskManager } from './RiskManager';
import { PositionSizingCalculator } from './PositionSizingCalculator';
import { AccountInfo, RiskParameters, TradeRequest } from '@/types/trading';

export interface ExecutionConfig {
  autoExecuteSignals: boolean;
  minConfidenceThreshold: number;
  maxSignalsPerHour: number;
  enableStopLoss: boolean;
  enableTakeProfit: boolean;
  defaultRiskPercentage: number;
  signalTypes: ('TECHNICAL' | 'AI_PREDICTION' | 'SENTIMENT')[];
}

export class TradeExecutionEngine {
  private broker: BrokerInterface;
  private riskManager: RiskManager;
  private calculator: PositionSizingCalculator;
  private config: ExecutionConfig;
  private executionHistory: TradeExecution[] = [];
  private lastExecutionTime: Map<string, Date> = new Map();

  constructor(
    broker: BrokerInterface,
    account: AccountInfo,
    riskParams: RiskParameters,
    config: ExecutionConfig
  ) {
    this.broker = broker;
    this.riskManager = new RiskManager(account, riskParams);
    this.calculator = new PositionSizingCalculator(account, riskParams);
    this.config = config;
  }

  /**
   * Process and potentially execute a trading signal
   */
  async processSignal(signal: MarketSignal): Promise<TradeExecution> {
    console.log(`Processing signal: ${signal.id} - ${signal.symbol} ${signal.signal}`);

    const execution: TradeExecution = {
      signalId: signal.id,
      symbol: signal.symbol,
      signalType: signal.signal,
      confidence: signal.confidence,
      suggestedQuantity: 0,
      suggestedPrice: signal.price,
      stopLoss: signal.stopLoss,
      takeProfit: signal.targetPrice,
      executionStatus: 'PENDING',
      reason: ''
    };

    try {
      // Pre-execution filters
      if (!this.shouldExecuteSignal(signal)) {
        execution.executionStatus = 'SKIPPED';
        execution.reason = this.getSkipReason(signal);
        this.executionHistory.push(execution);
        return execution;
      }

      // Calculate position size
      const positionSize = this.calculatePositionSize(signal);
      execution.suggestedQuantity = positionSize.quantity;

      if (positionSize.quantity === 0) {
        execution.executionStatus = 'SKIPPED';
        execution.reason = 'Position size calculation resulted in zero quantity';
        this.executionHistory.push(execution);
        return execution;
      }

      // Risk validation
      const tradeRequest = this.createTradeRequest(signal, positionSize.quantity);
      const riskValidation = this.riskManager.validateTrade(tradeRequest);

      if (!riskValidation.isValid) {
        execution.executionStatus = 'SKIPPED';
        execution.reason = `Risk validation failed: ${riskValidation.errors.join(', ')}`;
        this.executionHistory.push(execution);
        return execution;
      }

      // Execute trade if auto-execution is enabled
      if (this.config.autoExecuteSignals) {
        const orderRequest = this.convertToOrderRequest(tradeRequest);
        const orderResult = await this.executeTrade(orderRequest);
        
        if (orderResult.success && orderResult.data) {
          execution.executionStatus = 'EXECUTED';
          execution.orderId = orderResult.data.id;
          execution.executedAt = new Date();
          execution.reason = 'Successfully executed based on signal';
          
          // Update last execution time
          this.lastExecutionTime.set(signal.symbol, new Date());
        } else {
          execution.executionStatus = 'FAILED';
          execution.reason = orderResult.error || 'Trade execution failed';
        }
      } else {
        execution.executionStatus = 'PENDING';
        execution.reason = 'Auto-execution disabled - manual confirmation required';
      }

      this.executionHistory.push(execution);
      console.log(`Signal processed: ${execution.executionStatus} - ${execution.reason}`);
      
      return execution;

    } catch (error) {
      execution.executionStatus = 'FAILED';
      execution.reason = `Execution error: ${error}`;
      this.executionHistory.push(execution);
      console.error(`Error processing signal ${signal.id}:`, error);
      return execution;
    }
  }

  /**
   * Manually execute a trade based on signal
   */
  async executeSignalManually(signalId: string): Promise<TradeExecution> {
    const execution = this.executionHistory.find(e => e.signalId === signalId);
    if (!execution) {
      throw new Error('Signal execution not found');
    }

    if (execution.executionStatus === 'EXECUTED') {
      throw new Error('Signal already executed');
    }

    const orderRequest: OrderRequest = {
      symbol: execution.symbol,
      side: execution.signalType === 'BUY' ? 'BUY' : 'SELL',
      type: 'MARKET',
      quantity: execution.suggestedQuantity,
      stopLoss: execution.stopLoss,
      takeProfit: execution.takeProfit
    };

    const orderResult = await this.executeTrade(orderRequest);
    
    if (orderResult.success && orderResult.data) {
      execution.executionStatus = 'EXECUTED';
      execution.orderId = orderResult.data.id;
      execution.executedAt = new Date();
      execution.reason = 'Manually executed';
    } else {
      execution.executionStatus = 'FAILED';
      execution.reason = orderResult.error || 'Manual execution failed';
    }

    return execution;
  }

  /**
   * Check if signal should be executed based on filters
   */
  private shouldExecuteSignal(signal: MarketSignal): boolean {
    // Check signal type filter (filter out FUNDAMENTAL if present)
    const allowedType = signal.type === 'FUNDAMENTAL' ? false : this.config.signalTypes.includes(signal.type as any);
    if (!allowedType) {
      return false;
    }

    // Check confidence threshold
    if (signal.confidence < this.config.minConfidenceThreshold) {
      return false;
    }

    // Check signal direction (skip HOLD signals)
    if (signal.signal === 'HOLD') {
      return false;
    }

    // Check rate limiting
    const lastExecution = this.lastExecutionTime.get(signal.symbol);
    if (lastExecution) {
      const hoursSinceLastExecution = (Date.now() - lastExecution.getTime()) / (1000 * 60 * 60);
      if (hoursSinceLastExecution < (1 / this.config.maxSignalsPerHour)) {
        return false;
      }
    }

    // Check if signal is expired
    if (signal.expiresAt && signal.expiresAt.getTime() < Date.now()) {
      return false;
    }

    return true;
  }

  /**
   * Get reason for skipping signal
   */
  private getSkipReason(signal: MarketSignal): string {
    const allowedType = signal.type === 'FUNDAMENTAL' ? false : this.config.signalTypes.includes(signal.type as any);
    if (!allowedType) {
      return `Signal type ${signal.type} not enabled`;
    }
    
    if (signal.confidence < this.config.minConfidenceThreshold) {
      return `Confidence ${signal.confidence}% below threshold ${this.config.minConfidenceThreshold}%`;
    }
    
    if (signal.signal === 'HOLD') {
      return 'HOLD signals are not executed';
    }
    
    const lastExecution = this.lastExecutionTime.get(signal.symbol);
    if (lastExecution) {
      const hoursSinceLastExecution = (Date.now() - lastExecution.getTime()) / (1000 * 60 * 60);
      if (hoursSinceLastExecution < (1 / this.config.maxSignalsPerHour)) {
        return `Rate limited - last execution ${hoursSinceLastExecution.toFixed(1)} hours ago`;
      }
    }
    
    if (signal.expiresAt && signal.expiresAt.getTime() < Date.now()) {
      return 'Signal expired';
    }
    
    return 'Unknown reason';
  }

  /**
   * Calculate position size for signal
   */
  private calculatePositionSize(signal: MarketSignal): { quantity: number; risk: number } {
    if (!signal.stopLoss) {
      // If no stop loss, use default 2% risk with 2% stop
      const estimatedStopLoss = signal.price * (signal.signal === 'BUY' ? 0.98 : 1.02);
      const result = this.calculator.calculateFixedPercentageSize(
        signal.price,
        estimatedStopLoss,
        this.config.defaultRiskPercentage
      );
      return { quantity: result.quantity, risk: result.riskAmount };
    }

    const result = this.calculator.calculateFixedPercentageSize(
      signal.price,
      signal.stopLoss,
      this.config.defaultRiskPercentage
    );
    
    return { quantity: result.quantity, risk: result.riskAmount };
  }

  /**
   * Create trade request from signal
   */
  private createTradeRequest(signal: MarketSignal, quantity: number): TradeRequest {
    return {
      symbol: signal.symbol,
      side: signal.signal === 'BUY' ? 'BUY' : 'SELL',
      quantity,
      price: signal.price,
      stopLoss: this.config.enableStopLoss ? signal.stopLoss : undefined,
      takeProfit: this.config.enableTakeProfit ? signal.targetPrice : undefined,
      orderType: 'MARKET'
    };
  }

  /**
   * Convert TradeRequest to OrderRequest
   */
  private convertToOrderRequest(tradeRequest: TradeRequest): OrderRequest {
    return {
      symbol: tradeRequest.symbol,
      side: tradeRequest.side,
      type: tradeRequest.orderType === 'MARKET' ? 'MARKET' : 'LIMIT',
      quantity: tradeRequest.quantity,
      price: tradeRequest.orderType === 'LIMIT' ? tradeRequest.price : undefined,
      stopLoss: tradeRequest.stopLoss,
      takeProfit: tradeRequest.takeProfit
    };
  }

  /**
   * Execute trade through broker
   */
  private async executeTrade(orderRequest: OrderRequest) {
    console.log(`Executing trade: ${orderRequest.symbol} ${orderRequest.side} ${orderRequest.quantity}`);
    
    const result = await this.broker.placeOrder(orderRequest);
    
    // If main order is successful and we have stop loss/take profit, place those orders
    if (result.success && result.data) {
      if (orderRequest.stopLoss) {
        await this.placeStopLossOrder(orderRequest, result.data.id);
      }
      
      if (orderRequest.takeProfit) {
        await this.takeProfitOrder(orderRequest, result.data.id);
      }
    }
    
    return result;
  }

  /**
   * Place stop loss order
   */
  private async placeStopLossOrder(originalRequest: OrderRequest, parentOrderId: string) {
    if (!originalRequest.stopLoss) return;

    const stopLossOrder: OrderRequest = {
      symbol: originalRequest.symbol,
      side: originalRequest.side === 'BUY' ? 'SELL' : 'BUY',
      type: 'STOP',
      quantity: originalRequest.quantity,
      stopPrice: originalRequest.stopLoss
    };

    try {
      await this.broker.placeOrder(stopLossOrder);
      console.log(`Stop loss order placed for ${parentOrderId}`);
    } catch (error) {
      console.error(`Failed to place stop loss order:`, error);
    }
  }

  /**
   * Place take profit order
   */
  private async takeProfitOrder(originalRequest: OrderRequest, parentOrderId: string) {
    if (!originalRequest.takeProfit) return;

    const takeProfitOrder: OrderRequest = {
      symbol: originalRequest.symbol,
      side: originalRequest.side === 'BUY' ? 'SELL' : 'BUY',
      type: 'LIMIT',
      quantity: originalRequest.quantity,
      price: originalRequest.takeProfit
    };

    try {
      await this.broker.placeOrder(takeProfitOrder);
      console.log(`Take profit order placed for ${parentOrderId}`);
    } catch (error) {
      console.error(`Failed to place take profit order:`, error);
    }
  }

  /**
   * Get execution history
   */
  getExecutionHistory(limit?: number): TradeExecution[] {
    const history = [...this.executionHistory].reverse();
    return limit ? history.slice(0, limit) : history;
  }

  /**
   * Get execution statistics
   */
  getExecutionStats() {
    const total = this.executionHistory.length;
    const executed = this.executionHistory.filter(e => e.executionStatus === 'EXECUTED').length;
    const failed = this.executionHistory.filter(e => e.executionStatus === 'FAILED').length;
    const skipped = this.executionHistory.filter(e => e.executionStatus === 'SKIPPED').length;

    return {
      total,
      executed,
      failed,
      skipped,
      executionRate: total > 0 ? (executed / total) * 100 : 0,
      avgConfidence: total > 0 ? this.executionHistory.reduce((sum, e) => sum + e.confidence, 0) / total : 0
    };
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<ExecutionConfig>) {
    this.config = { ...this.config, ...newConfig };
    console.log('Execution config updated:', this.config);
  }
}

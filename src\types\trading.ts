
export interface Position {
  symbol: string;
  quantity: number;
  averagePrice: number;
  currentPrice: number;
  unrealizedPnL: number;
  sector: string;
  marketValue: number;
}

export interface AccountInfo {
  totalBalance: number;
  availableBalance: number;
  usedMargin: number;
  dailyPnL: number;
  totalPnL: number;
  positions: Position[];
}

export interface RiskParameters {
  maxRiskPerTrade: number; // percentage (default: 2%)
  maxPortfolioRisk: number; // percentage (default: 10%)
  maxSectorExposure: number; // percentage (default: 10%)
  maxSingleStockExposure: number; // percentage (default: 5%)
  dailyLossLimit: number; // absolute amount
  maxDrawdown: number; // percentage (default: 15%)
  minRiskRewardRatio: number; // default: 2.0
}

export interface TradeRequest {
  symbol: string;
  side: 'BUY' | 'SELL';
  quantity: number;
  price: number;
  stopLoss?: number;
  takeProfit?: number;
  orderType: 'MARKET' | 'LIMIT';
}

export interface RiskValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  suggestedQuantity?: number;
  riskAmount: number;
  positionSize: number;
}

export interface PositionSizeResult {
  quantity: number;
  riskAmount: number;
  positionValue: number;
  riskPercentage: number;
}

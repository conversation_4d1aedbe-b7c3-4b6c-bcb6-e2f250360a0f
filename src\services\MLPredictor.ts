
import { MLPrediction, MarketDataPoint } from '@/types/ai';
import { OHLCV } from '@/types/signals';

export class MLPredictor {
  private static readonly FEATURE_WEIGHTS = {
    rsi: 0.15,
    macd: 0.12,
    bollinger: 0.10,
    volume: 0.08,
    volatility: 0.08,
    sentiment: 0.20,
    momentum: 0.12,
    support_resistance: 0.10,
    news_count: 0.05
  };

  /**
   * Generate ML-based price predictions
   */
  static async generatePrediction(
    symbol: string,
    ohlcvData: OHLCV[],
    marketData: MarketDataPoint[],
    timeframe: '1h' | '4h' | '1d' = '1d'
  ): Promise<MLPrediction> {
    try {
      // Extract features
      const features = this.extractFeatures(ohlcvData, marketData);
      
      // Simple ML model simulation (in production, this would call a real ML service)
      const prediction = this.simulateMLModel(features, timeframe);
      
      return {
        symbol,
        timeframe,
        prediction,
        features,
        model: 'LSTM_v1.0',
        timestamp: new Date()
      };
    } catch (error) {
      console.error('Error generating ML prediction:', error);
      throw error;
    }
  }

  /**
   * Extract features for ML model
   */
  private static extractFeatures(ohlcvData: OHLCV[], marketData: MarketDataPoint[]) {
    const prices = ohlcvData.map(d => d.close);
    const volumes = ohlcvData.map(d => d.volume);
    
    // Technical indicators
    const rsi = this.calculateRSI(prices);
    const macd = this.calculateMACD(prices);
    const bollinger = this.calculateBollingerPosition(prices);
    const volumeRatio = this.calculateVolumeRatio(volumes);
    const volatility = this.calculateVolatility(prices);
    const momentum = this.calculateMomentum(prices);
    const supportResistance = this.calculateSupportResistanceStrength(ohlcvData);
    
    // Sentiment features
    const latestMarketData = marketData[marketData.length - 1];
    const sentiment = latestMarketData ? latestMarketData.sentiment.polarity : 0;
    const newsCount = latestMarketData ? latestMarketData.newsCount : 0;
    
    return {
      technical: [rsi, macd, bollinger, volumeRatio, volatility, momentum, supportResistance],
      sentiment: sentiment,
      volume: volumeRatio,
      volatility: volatility
    };
  }

  /**
   * Simulate ML model prediction
   */
  private static simulateMLModel(features: any, timeframe: string) {
    // Weighted feature combination
    const technicalScore = features.technical.reduce((sum: number, value: number, index: number) => {
      const weights = [0.25, 0.20, 0.15, 0.10, 0.10, 0.15, 0.05];
      return sum + (value * weights[index]);
    }, 0);
    
    const sentimentScore = features.sentiment * 0.3;
    const volatilityScore = (1 - features.volatility) * 0.2; // Lower volatility = higher confidence
    
    const combinedScore = (technicalScore + sentimentScore + volatilityScore) / 3;
    
    // Determine direction and confidence
    let direction: 'UP' | 'DOWN' | 'SIDEWAYS' = 'SIDEWAYS';
    let confidence = Math.abs(combinedScore);
    
    if (combinedScore > 0.1) {
      direction = 'UP';
    } else if (combinedScore < -0.1) {
      direction = 'DOWN';
    }
    
    // Adjust confidence based on timeframe
    const timeframeMultiplier = timeframe === '1h' ? 0.8 : timeframe === '4h' ? 0.9 : 1.0;
    confidence = Math.min(0.95, confidence * timeframeMultiplier);
    
    // Calculate target price (simplified)
    const currentPrice = 15000; // Would be actual current price
    const priceChange = combinedScore * 0.05; // 5% max change
    const targetPrice = currentPrice * (1 + priceChange);
    
    return {
      direction,
      confidence,
      targetPrice,
      probability: confidence * 0.8 // Probability is slightly lower than confidence
    };
  }

  // Technical indicator calculations (simplified versions)
  private static calculateRSI(prices: number[]): number {
    if (prices.length < 14) return 0.5;
    
    let gains = 0;
    let losses = 0;
    
    for (let i = 1; i < Math.min(15, prices.length); i++) {
      const change = prices[i] - prices[i - 1];
      if (change > 0) gains += change;
      else losses += Math.abs(change);
    }
    
    const avgGain = gains / 14;
    const avgLoss = losses / 14;
    const rs = avgGain / avgLoss;
    const rsi = 100 - (100 / (1 + rs));
    
    return (rsi - 50) / 50; // Normalize to -1 to 1
  }

  private static calculateMACD(prices: number[]): number {
    if (prices.length < 26) return 0;
    
    const ema12 = this.calculateEMA(prices.slice(-12), 12);
    const ema26 = this.calculateEMA(prices.slice(-26), 26);
    
    return (ema12 - ema26) / ema26; // Normalized
  }

  private static calculateEMA(prices: number[], period: number): number {
    const multiplier = 2 / (period + 1);
    let ema = prices[0];
    
    for (let i = 1; i < prices.length; i++) {
      ema = (prices[i] * multiplier) + (ema * (1 - multiplier));
    }
    
    return ema;
  }

  private static calculateBollingerPosition(prices: number[]): number {
    if (prices.length < 20) return 0;
    
    const recentPrices = prices.slice(-20);
    const sma = recentPrices.reduce((sum, price) => sum + price, 0) / 20;
    const variance = recentPrices.reduce((sum, price) => sum + Math.pow(price - sma, 2), 0) / 20;
    const stdDev = Math.sqrt(variance);
    
    const currentPrice = prices[prices.length - 1];
    const position = (currentPrice - sma) / (2 * stdDev);
    
    return Math.max(-1, Math.min(1, position));
  }

  private static calculateVolumeRatio(volumes: number[]): number {
    if (volumes.length < 20) return 1;
    
    const recentVolumes = volumes.slice(-20);
    const avgVolume = recentVolumes.reduce((sum, vol) => sum + vol, 0) / 20;
    const currentVolume = volumes[volumes.length - 1];
    
    return Math.min(3, currentVolume / avgVolume) / 3; // Normalize to 0-1
  }

  private static calculateVolatility(prices: number[]): number {
    if (prices.length < 20) return 0.5;
    
    const returns = [];
    for (let i = 1; i < prices.length; i++) {
      returns.push((prices[i] - prices[i - 1]) / prices[i - 1]);
    }
    
    const avgReturn = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
    const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - avgReturn, 2), 0) / returns.length;
    const volatility = Math.sqrt(variance);
    
    return Math.min(1, volatility * 100); // Normalize
  }

  private static calculateMomentum(prices: number[]): number {
    if (prices.length < 10) return 0;
    
    const shortMA = prices.slice(-5).reduce((sum, price) => sum + price, 0) / 5;
    const longMA = prices.slice(-10).reduce((sum, price) => sum + price, 0) / 10;
    
    return (shortMA - longMA) / longMA;
  }

  private static calculateSupportResistanceStrength(ohlcvData: OHLCV[]): number {
    if (ohlcvData.length < 20) return 0.5;
    
    const recentData = ohlcvData.slice(-20);
    const highs = recentData.map(d => d.high);
    const lows = recentData.map(d => d.low);
    const currentPrice = recentData[recentData.length - 1].close;
    
    const resistance = Math.max(...highs);
    const support = Math.min(...lows);
    const range = resistance - support;
    
    if (range === 0) return 0.5;
    
    const position = (currentPrice - support) / range;
    return Math.abs(position - 0.5) * 2; // Distance from middle
  }
}

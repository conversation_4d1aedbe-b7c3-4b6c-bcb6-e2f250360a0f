import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Volume2, VolumeX, Mic, Settings, CheckCircle, XCircle, Loader2 } from 'lucide-react';
import { ElevenLabsService } from '@/services/ElevenLabsService';
import { useToast } from '@/hooks/use-toast';

interface Voice {
  voice_id: string;
  name: string;
  description?: string;
  category: string;
}

const SAMPLE_VOICES = [
  { voice_id: 'EXAVITQu4vr4xnSDxMaL', name: '<PERSON>', description: 'Professional female voice', category: 'news' },
  { voice_id: '9BWtsMINqrJLrRacOk9x', name: 'Aria', description: 'Expressive female voice', category: 'narration' },
  { voice_id: 'CwhRBWXzGAHq8TQ4Fs17', name: 'Roger', description: 'Mature male voice', category: 'news' },
  { voice_id: 'TX3LPaxmHKxFdv7VOQHJ', name: 'Liam', description: 'Young male voice', category: 'conversational' },
  { voice_id: 'XB0fDUnXU5powFXDhCwa', name: 'Charlotte', description: 'Friendly female voice', category: 'conversational' },
  { voice_id: 'pFZP5JQG7iQjIQuC4Bku', name: 'Lily', description: 'Sweet female voice', category: 'conversational' },
  { voice_id: 'onwK4e9ZLuTAKqWW03F9', name: 'Daniel', description: 'Professional male voice', category: 'news' }
];

const MODELS = [
  { id: 'eleven_turbo_v2_5', name: 'Turbo v2.5', description: 'High quality, low latency (32 languages)' },
  { id: 'eleven_turbo_v2', name: 'Turbo v2', description: 'English-only, low latency' },
  { id: 'eleven_multilingual_v2', name: 'Multilingual v2', description: 'Most life-like, emotionally rich (29 languages)' },
  { id: 'eleven_multilingual_v1', name: 'Multilingual v1', description: 'First multilingual model (10 languages)' }
];

export const ElevenLabsSetup: React.FC = () => {
  const [apiKey, setApiKey] = useState('');
  const [selectedVoiceId, setSelectedVoiceId] = useState('EXAVITQu4vr4xnSDxMaL');
  const [selectedModel, setSelectedModel] = useState('eleven_turbo_v2_5');
  const [voices, setVoices] = useState<Voice[]>(SAMPLE_VOICES);
  const [isConfigured, setIsConfigured] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [stability, setStability] = useState(0.5);
  const [similarityBoost, setSimilarityBoost] = useState(0.5);
  const [style, setStyle] = useState(0.0);
  
  const { toast } = useToast();

  useEffect(() => {
    // Check if already configured
    const config = ElevenLabsService.getConfig();
    setIsConfigured(config.hasApiKey);
    setSelectedVoiceId(config.voiceId);
    setSelectedModel(config.model);
    
    // Load stored API key
    const storedKey = localStorage.getItem('elevenlabs_api_key');
    if (storedKey) {
      setApiKey(storedKey);
    }
  }, []);

  const handleSaveConfiguration = async () => {
    if (!apiKey.trim()) {
      toast({
        title: "API Key Required",
        description: "Please enter your ElevenLabs API key",
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);
    
    try {
      // Initialize ElevenLabs service
      ElevenLabsService.initialize({
        apiKey: apiKey.trim(),
        voiceId: selectedVoiceId,
        model: selectedModel
      });
      
      // Test the configuration by fetching voices
      const fetchedVoices = await ElevenLabsService.getVoices();
      if (fetchedVoices.length > 0) {
        setVoices(fetchedVoices);
      }
      
      setIsConfigured(true);
      
      toast({
        title: "Configuration Saved",
        description: "ElevenLabs TTS is now configured and ready to use",
      });
      
    } catch (error) {
      console.error('Configuration error:', error);
      toast({
        title: "Configuration Error",
        description: "Failed to configure ElevenLabs. Please check your API key.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleTestVoice = async () => {
    if (!isConfigured) {
      toast({
        title: "Not Configured",
        description: "Please configure ElevenLabs first",
        variant: "destructive"
      });
      return;
    }

    setIsTesting(true);
    
    try {
      const testText = "Welcome to your intelligent trading assistant. Your orders and market analysis are now enhanced with advanced voice capabilities.";
      
      const audioBuffer = await ElevenLabsService.textToSpeech(testText, {
        voiceId: selectedVoiceId,
        model: selectedModel,
        voiceSettings: {
          stability,
          similarity_boost: similarityBoost,
          style,
          use_speaker_boost: true
        }
      });
      
      await ElevenLabsService.playAudio(audioBuffer);
      
      toast({
        title: "Voice Test Successful",
        description: "Voice synthesis is working correctly",
      });
      
    } catch (error) {
      console.error('Voice test error:', error);
      toast({
        title: "Voice Test Failed",
        description: "Failed to test voice synthesis",
        variant: "destructive"
      });
    } finally {
      setIsTesting(false);
    }
  };

  const handleDisconnect = () => {
    localStorage.removeItem('elevenlabs_api_key');
    localStorage.removeItem('elevenlabs_voice_id');
    localStorage.removeItem('elevenlabs_model');
    setApiKey('');
    setIsConfigured(false);
    
    toast({
      title: "Disconnected",
      description: "ElevenLabs configuration has been removed",
    });
  };

  return (
    <Card className="w-full backdrop-blur-md bg-card/50 border-primary/20">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Volume2 className="h-5 w-5" />
          ElevenLabs Text-to-Speech Setup
          {isConfigured && (
            <Badge variant="default" className="ml-auto">
              <CheckCircle className="h-3 w-3 mr-1" />
              Configured
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      
      <CardContent>
        <Tabs defaultValue="setup" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="setup">Setup</TabsTrigger>
            <TabsTrigger value="voices">Voices</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>
          
          <TabsContent value="setup" className="space-y-4">
            {!isConfigured ? (
              <div className="space-y-4">
                <div className="p-4 bg-blue-50 dark:bg-blue-950 rounded-lg">
                  <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">
                    Get Your ElevenLabs API Key
                  </h3>
                  <p className="text-sm text-blue-800 dark:text-blue-200 mb-2">
                    1. Visit <a href="https://elevenlabs.io" target="_blank" rel="noopener noreferrer" className="underline">elevenlabs.io</a> and create an account
                  </p>
                  <p className="text-sm text-blue-800 dark:text-blue-200 mb-2">
                    2. Go to your Profile Settings → API Keys
                  </p>
                  <p className="text-sm text-blue-800 dark:text-blue-200">
                    3. Copy your API key and paste it below
                  </p>
                </div>
                
                <div>
                  <Label htmlFor="apiKey">ElevenLabs API Key</Label>
                  <Input
                    id="apiKey"
                    type="password"
                    value={apiKey}
                    onChange={(e) => setApiKey(e.target.value)}
                    placeholder="Enter your ElevenLabs API key"
                  />
                </div>
                
                <div>
                  <Label htmlFor="model">Model</Label>
                  <Select value={selectedModel} onValueChange={setSelectedModel}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {MODELS.map((model) => (
                        <SelectItem key={model.id} value={model.id}>
                          <div>
                            <div className="font-medium">{model.name}</div>
                            <div className="text-xs text-muted-foreground">{model.description}</div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label htmlFor="voice">Default Voice</Label>
                  <Select value={selectedVoiceId} onValueChange={setSelectedVoiceId}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {SAMPLE_VOICES.map((voice) => (
                        <SelectItem key={voice.voice_id} value={voice.voice_id}>
                          <div>
                            <div className="font-medium">{voice.name}</div>
                            <div className="text-xs text-muted-foreground">{voice.description}</div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <Button 
                  onClick={handleSaveConfiguration} 
                  disabled={isLoading || !apiKey.trim()}
                  className="w-full"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Configuring...
                    </>
                  ) : (
                    <>
                      <Settings className="h-4 w-4 mr-2" />
                      Save Configuration
                    </>
                  )}
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="p-4 bg-green-50 dark:bg-green-950 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <h3 className="font-semibold text-green-900 dark:text-green-100">
                      ElevenLabs Configured
                    </h3>
                  </div>
                  <p className="text-sm text-green-800 dark:text-green-200">
                    Your trading assistant can now speak! Voice confirmations and alerts are enabled.
                  </p>
                </div>
                
                <div className="flex gap-2">
                  <Button onClick={handleTestVoice} disabled={isTesting} variant="outline">
                    {isTesting ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Testing...
                      </>
                    ) : (
                      <>
                        <Mic className="h-4 w-4 mr-2" />
                        Test Voice
                      </>
                    )}
                  </Button>
                  
                  <Button onClick={handleDisconnect} variant="destructive">
                    <XCircle className="h-4 w-4 mr-2" />
                    Disconnect
                  </Button>
                </div>
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="voices" className="space-y-4">
            <div className="grid grid-cols-1 gap-3">
              {voices.map((voice) => (
                <div
                  key={voice.voice_id}
                  className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                    selectedVoiceId === voice.voice_id
                      ? 'border-primary bg-primary/5'
                      : 'border-border hover:bg-muted/50'
                  }`}
                  onClick={() => setSelectedVoiceId(voice.voice_id)}
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-medium">{voice.name}</h3>
                      {voice.description && (
                        <p className="text-sm text-muted-foreground">{voice.description}</p>
                      )}
                      <Badge variant="outline" className="mt-1 text-xs">
                        {voice.category || 'general'}
                      </Badge>
                    </div>
                    {selectedVoiceId === voice.voice_id && (
                      <CheckCircle className="h-4 w-4 text-primary" />
                    )}
                  </div>
                </div>
              ))}
            </div>
            
            {isConfigured && (
              <Button onClick={handleTestVoice} disabled={isTesting} className="w-full">
                {isTesting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Testing Selected Voice...
                  </>
                ) : (
                  <>
                    <Volume2 className="h-4 w-4 mr-2" />
                    Test Selected Voice
                  </>
                )}
              </Button>
            )}
          </TabsContent>
          
          <TabsContent value="settings" className="space-y-4">
            <div className="space-y-4">
              <div>
                <Label>Stability: {stability}</Label>
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  value={stability}
                  onChange={(e) => setStability(Number(e.target.value))}
                  className="w-full"
                />
                <p className="text-xs text-muted-foreground">
                  Higher values make the voice more consistent but less expressive
                </p>
              </div>
              
              <div>
                <Label>Similarity Boost: {similarityBoost}</Label>
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  value={similarityBoost}
                  onChange={(e) => setSimilarityBoost(Number(e.target.value))}
                  className="w-full"
                />
                <p className="text-xs text-muted-foreground">
                  Higher values make the voice more similar to the original speaker
                </p>
              </div>
              
              <div>
                <Label>Style: {style}</Label>
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  value={style}
                  onChange={(e) => setStyle(Number(e.target.value))}
                  className="w-full"
                />
                <p className="text-xs text-muted-foreground">
                  Controls the style and emotion of the generated speech
                </p>
              </div>
            </div>
            
            <div className="p-4 bg-muted rounded-lg">
              <h3 className="font-semibold mb-2">Usage in Trading</h3>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• Order confirmations and alerts</li>
                <li>• Market analysis summaries</li>
                <li>• Risk warnings and notifications</li>
                <li>• Portfolio updates</li>
                <li>• Voice-guided tutorials</li>
              </ul>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};
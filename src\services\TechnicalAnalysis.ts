
import { TechnicalIndicator, MarketSignal } from '@/types/signals';

export interface OHLCV {
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  timestamp: Date;
}

export class TechnicalAnalysis {
  /**
   * Calculate RSI (Relative Strength Index)
   */
  static calculateRSI(prices: number[], period: number = 14): TechnicalIndicator {
    if (prices.length < period + 1) {
      throw new Error(`Insufficient data for RSI calculation. Need ${period + 1} prices, got ${prices.length}`);
    }

    let gains = 0;
    let losses = 0;

    // Calculate initial average gain and loss
    for (let i = 1; i <= period; i++) {
      const change = prices[i] - prices[i - 1];
      if (change > 0) gains += change;
      else losses += Math.abs(change);
    }

    let avgGain = gains / period;
    let avgLoss = losses / period;

    // Calculate RSI for remaining periods
    for (let i = period + 1; i < prices.length; i++) {
      const change = prices[i] - prices[i - 1];
      const gain = change > 0 ? change : 0;
      const loss = change < 0 ? Math.abs(change) : 0;

      avgGain = ((avgGain * (period - 1)) + gain) / period;
      avgLoss = ((avgLoss * (period - 1)) + loss) / period;
    }

    const rs = avgGain / avgLoss;
    const rsi = 100 - (100 / (1 + rs));

    let signal: 'BUY' | 'SELL' | 'HOLD' = 'HOLD';
    let strength = 50;

    if (rsi < 30) {
      signal = 'BUY';
      strength = Math.max(70, 100 - rsi);
    } else if (rsi > 70) {
      signal = 'SELL';
      strength = Math.max(70, rsi);
    }

    return {
      name: 'RSI',
      value: rsi,
      signal,
      strength,
      timestamp: new Date()
    };
  }

  /**
   * Calculate MACD (Moving Average Convergence Divergence)
   */
  static calculateMACD(prices: number[], fastPeriod: number = 12, slowPeriod: number = 26, signalPeriod: number = 9): TechnicalIndicator {
    if (prices.length < slowPeriod) {
      throw new Error(`Insufficient data for MACD calculation`);
    }

    // Calculate EMAs
    const fastEMA = this.calculateEMA(prices, fastPeriod);
    const slowEMA = this.calculateEMA(prices, slowPeriod);
    
    // Calculate MACD line
    const macdLine = fastEMA - slowEMA;
    
    // For signal line, we'd need historical MACD values
    // Simplified version for demonstration
    let signal: 'BUY' | 'SELL' | 'HOLD' = 'HOLD';
    let strength = 50;

    if (macdLine > 0) {
      signal = 'BUY';
      strength = Math.min(90, 60 + Math.abs(macdLine) * 10);
    } else if (macdLine < 0) {
      signal = 'SELL';
      strength = Math.min(90, 60 + Math.abs(macdLine) * 10);
    }

    return {
      name: 'MACD',
      value: macdLine,
      signal,
      strength,
      timestamp: new Date()
    };
  }

  /**
   * Calculate Bollinger Bands
   */
  static calculateBollingerBands(prices: number[], period: number = 20, stdDev: number = 2): TechnicalIndicator {
    if (prices.length < period) {
      throw new Error(`Insufficient data for Bollinger Bands calculation`);
    }

    const recentPrices = prices.slice(-period);
    const sma = recentPrices.reduce((sum, price) => sum + price, 0) / period;
    
    const variance = recentPrices.reduce((sum, price) => sum + Math.pow(price - sma, 2), 0) / period;
    const standardDeviation = Math.sqrt(variance);

    const upperBand = sma + (standardDeviation * stdDev);
    const lowerBand = sma - (standardDeviation * stdDev);
    const currentPrice = prices[prices.length - 1];

    let signal: 'BUY' | 'SELL' | 'HOLD' = 'HOLD';
    let strength = 50;

    if (currentPrice <= lowerBand) {
      signal = 'BUY';
      strength = Math.min(95, 70 + ((lowerBand - currentPrice) / lowerBand) * 100);
    } else if (currentPrice >= upperBand) {
      signal = 'SELL';
      strength = Math.min(95, 70 + ((currentPrice - upperBand) / upperBand) * 100);
    }

    return {
      name: 'Bollinger Bands',
      value: (currentPrice - sma) / standardDeviation, // Position relative to bands
      signal,
      strength,
      timestamp: new Date()
    };
  }

  /**
   * Calculate Exponential Moving Average
   */
  private static calculateEMA(prices: number[], period: number): number {
    const multiplier = 2 / (period + 1);
    let ema = prices[0];

    for (let i = 1; i < prices.length; i++) {
      ema = (prices[i] * multiplier) + (ema * (1 - multiplier));
    }

    return ema;
  }

  /**
   * Support and Resistance Level Detection
   */
  static findSupportResistance(ohlcvData: OHLCV[], lookback: number = 20): { support: number; resistance: number; strength: number } {
    if (ohlcvData.length < lookback) {
      throw new Error('Insufficient data for support/resistance calculation');
    }

    const recentData = ohlcvData.slice(-lookback);
    const highs = recentData.map(d => d.high);
    const lows = recentData.map(d => d.low);

    // Find local maxima and minima
    const resistance = Math.max(...highs);
    const support = Math.min(...lows);
    
    const currentPrice = recentData[recentData.length - 1].close;
    const range = resistance - support;
    const position = (currentPrice - support) / range;

    // Calculate strength based on how many times price touched these levels
    const touchCount = recentData.filter(d => 
      Math.abs(d.high - resistance) < (range * 0.01) || 
      Math.abs(d.low - support) < (range * 0.01)
    ).length;

    const strength = Math.min(100, touchCount * 15);

    return { support, resistance, strength };
  }

  /**
   * Volume Analysis
   */
  static analyzeVolume(ohlcvData: OHLCV[], period: number = 20): TechnicalIndicator {
    if (ohlcvData.length < period) {
      throw new Error('Insufficient data for volume analysis');
    }

    const recentData = ohlcvData.slice(-period);
    const avgVolume = recentData.reduce((sum, d) => sum + d.volume, 0) / period;
    const currentVolume = recentData[recentData.length - 1].volume;
    const currentPrice = recentData[recentData.length - 1].close;
    const prevPrice = recentData[recentData.length - 2].close;

    const volumeRatio = currentVolume / avgVolume;
    const priceChange = (currentPrice - prevPrice) / prevPrice;

    let signal: 'BUY' | 'SELL' | 'HOLD' = 'HOLD';
    let strength = 50;

    // High volume with price increase = bullish
    if (volumeRatio > 1.5 && priceChange > 0.01) {
      signal = 'BUY';
      strength = Math.min(90, 50 + (volumeRatio * 20) + (priceChange * 1000));
    }
    // High volume with price decrease = bearish
    else if (volumeRatio > 1.5 && priceChange < -0.01) {
      signal = 'SELL';
      strength = Math.min(90, 50 + (volumeRatio * 20) + (Math.abs(priceChange) * 1000));
    }

    return {
      name: 'Volume Analysis',
      value: volumeRatio,
      signal,
      strength,
      timestamp: new Date()
    };
  }
}

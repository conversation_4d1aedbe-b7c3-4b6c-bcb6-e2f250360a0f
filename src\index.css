@tailwind base;
@tailwind components;
@tailwind utilities;

/* Modern Trading Platform Design System with Glassmorphism */

@layer base {
  :root {
    /* Base colors */
    --background: 220 20% 4%;
    --foreground: 210 40% 98%;
    --card: 220 13% 8%;
    --card-foreground: 210 40% 98%;
    --popover: 220 13% 8%;
    --popover-foreground: 210 40% 98%;
    
    /* Primary brand colors */
    --primary: 217 91% 60%;
    --primary-foreground: 220 20% 4%;
    --secondary: 217 19% 27%;
    --secondary-foreground: 210 40% 98%;
    
    /* UI element colors */
    --muted: 215 25% 16%;
    --muted-foreground: 217 10% 64.9%;
    --accent: 142 76% 36%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 215 25% 16%;
    --input: 215 25% 16%;
    --ring: 217 91% 60%;
    --radius: 0.75rem;

    /* Trading specific colors */
    --success: 142 76% 36%;
    --warning: 38 92% 50%;
    --info: 199 89% 48%;
    --buy: 142 76% 36%;
    --sell: 0 84% 60%;
    --hold: 38 92% 50%;

    /* Glassmorphism effects */
    --glass-bg: 220 13% 8% / 0.8;
    --glass-border: 217 91% 60% / 0.2;
    --glass-blur: blur(16px);
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(217 91% 60%), hsl(142 76% 36%));
    --gradient-secondary: linear-gradient(135deg, hsl(220 13% 8%), hsl(215 25% 16%));
    --gradient-hero: linear-gradient(135deg, hsl(220 20% 4%), hsl(217 19% 27%));
    --gradient-card: linear-gradient(135deg, hsl(220 13% 8% / 0.8), hsl(215 25% 16% / 0.6));
    
    /* Shadows and glows */
    --shadow-glow: 0 0 40px hsl(217 91% 60% / 0.3);
    --shadow-card: 0 8px 32px hsl(220 20% 4% / 0.8);
    --shadow-floating: 0 16px 48px hsl(220 20% 4% / 0.9);
    --shadow-success: 0 0 30px hsl(142 76% 36% / 0.4);
    --shadow-danger: 0 0 30px hsl(0 84% 60% / 0.4);

    /* Sidebar */
    --sidebar-background: 220 13% 8%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 217 91% 60%;
    --sidebar-primary-foreground: 220 20% 4%;
    --sidebar-accent: 217 19% 27%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 215 25% 16%;
    --sidebar-ring: 217 91% 60%;

    /* Chart colors */
    --chart-1: 142 76% 36%;
    --chart-2: 217 91% 60%;
    --chart-3: 38 92% 50%;
    --chart-4: 0 84% 60%;
    --chart-5: 199 89% 48%;
  }

  .light {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 217 91% 60%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 142 76% 36%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 217 91% 60%;

    /* Light mode glassmorphism */
    --glass-bg: 255 255% 100% / 0.9;
    --glass-border: 217 91% 60% / 0.3;
    --gradient-hero: linear-gradient(135deg, hsl(0 0% 100%), hsl(210 40% 96%));
    --gradient-card: linear-gradient(135deg, hsl(0 0% 100% / 0.9), hsl(210 40% 96% / 0.8));
  }
}

@layer components {
  /* Glassmorphism utilities */
  .glass {
    background: hsl(var(--glass-bg));
    backdrop-filter: var(--glass-blur);
    border: 1px solid hsl(var(--glass-border));
  }
  
  .glass-card {
    @apply glass rounded-xl shadow-card hover:shadow-floating transition-all duration-300;
  }
  
  .glass-button {
    @apply glass rounded-lg transition-all duration-300 hover:shadow-glow hover:scale-105;
  }
  
  /* Gradient backgrounds */
  .bg-gradient-primary {
    background: var(--gradient-primary);
  }
  
  .bg-gradient-secondary {
    background: var(--gradient-secondary);
  }
  
  .bg-gradient-hero {
    background: var(--gradient-hero);
  }
  
  .bg-gradient-card {
    background: var(--gradient-card);
  }
  
  /* Animation utilities */
  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }
  
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }
  
  .animate-pulse-slow {
    animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
  
  .animate-bounce-slow {
    animation: bounce 3s infinite;
  }
  
  /* Interactive effects */
  .hover-lift {
    @apply transition-all duration-300 hover:scale-105 hover:-translate-y-1;
  }
  
  .hover-glow {
    @apply transition-all duration-300 hover:shadow-glow;
  }
  
  .hover-success {
    @apply transition-all duration-300 hover:shadow-success;
  }
  
  .hover-danger {
    @apply transition-all duration-300 hover:shadow-danger;
  }
}

@layer utilities {
  /* Custom shadows */
  .shadow-glow {
    box-shadow: var(--shadow-glow);
  }
  
  .shadow-card {
    box-shadow: var(--shadow-card);
  }
  
  .shadow-floating {
    box-shadow: var(--shadow-floating);
  }
  
  .shadow-success {
    box-shadow: var(--shadow-success);
  }
  
  .shadow-danger {
    box-shadow: var(--shadow-danger);
  }
}

/* Keyframes for animations */
@keyframes glow {
  from {
    box-shadow: 0 0 20px hsl(var(--primary) / 0.5);
  }
  to {
    box-shadow: 0 0 40px hsl(var(--primary) / 0.8);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--primary));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary) / 0.8);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}
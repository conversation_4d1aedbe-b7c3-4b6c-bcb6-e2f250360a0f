
import React, { useState } from 'react';
import EnhancedLandingPage from '@/components/EnhancedLandingPage';
import TradingDashboard from '@/components/TradingDashboard';
import AuthModal from '@/components/AuthModal';
import PaymentModal from '@/components/PaymentModal';
import { useToast } from '@/hooks/use-toast';

interface User {
  email: string;
  name: string;
  subscribed?: boolean;
}

const Index = () => {
  const [user, setUser] = useState<User | null>(null);
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const { toast } = useToast();

  const handleGetStarted = () => {
    setShowAuthModal(true);
  };

  const handleAuthenticated = (userData: User) => {
    setUser(userData);
    setShowPaymentModal(true);
    toast({
      title: "Welcome!",
      description: `Welcome to Alpha Trade Flow AI, ${userData.name}!`,
    });
  };

  const handlePaymentSuccess = () => {
    if (user) {
      setUser({ ...user, subscribed: true });
      toast({
        title: "Payment Successful!",
        description: "Your subscription is now active. Welcome to the future of trading!",
      });
    }
  };

  // Show trading dashboard if user is authenticated and subscribed
  if (user?.subscribed) {
    return <TradingDashboard />;
  }

  // Show landing page
  return (
    <>
      <EnhancedLandingPage onGetStarted={handleGetStarted} />
      
      <AuthModal
        open={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        onAuthenticated={handleAuthenticated}
      />
      
      <PaymentModal
        open={showPaymentModal}
        onClose={() => setShowPaymentModal(false)}
        onPaymentSuccess={handlePaymentSuccess}
      />
    </>
  );
};

export default Index;

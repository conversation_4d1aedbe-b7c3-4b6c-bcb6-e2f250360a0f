
export interface Order {
  id: string;
  symbol: string;
  side: 'BUY' | 'SELL';
  type: 'MARKET' | 'LIMIT' | 'STOP' | 'STOP_LIMIT';
  quantity: number;
  price?: number;
  stopPrice?: number;
  timeInForce: 'GTC' | 'IOC' | 'FOK' | 'DAY';
  status: 'PENDING' | 'FILLED' | 'PARTIALLY_FILLED' | 'CANCELLED' | 'REJECTED';
  filledQuantity: number;
  filledPrice?: number;
  timestamp: Date;
  updatedAt: Date;
  fees: number;
}

export interface Trade {
  id: string;
  orderId: string;
  symbol: string;
  side: 'BUY' | 'SELL';
  quantity: number;
  price: number;
  fees: number;
  timestamp: Date;
  signalId?: string;
}

export interface BrokerAccount {
  accountId: string;
  balance: number;
  equity: number;
  availableBalance: number;
  usedMargin: number;
  freeMargin: number;
  marginLevel: number;
  unrealizedPnL: number;
  dailyPnL: number;
  positions: BrokerPosition[];
  orders: Order[];
  trades: Trade[];
}

export interface BrokerPosition {
  symbol: string;
  side: 'LONG' | 'SHORT';
  quantity: number;
  averagePrice: number;
  currentPrice: number;
  unrealizedPnL: number;
  realizedPnL: number;
  marketValue: number;
  timestamp: Date;
}

export interface OrderRequest {
  symbol: string;
  side: 'BUY' | 'SELL';
  type: 'MARKET' | 'LIMIT' | 'STOP' | 'STOP_LIMIT';
  quantity: number;
  price?: number;
  stopPrice?: number;
  timeInForce?: 'GTC' | 'IOC' | 'FOK' | 'DAY';
  stopLoss?: number;
  takeProfit?: number;
}

export interface BrokerResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface TradeExecution {
  signalId: string;
  symbol: string;
  signalType: 'BUY' | 'SELL' | 'HOLD';
  confidence: number;
  suggestedQuantity: number;
  suggestedPrice: number;
  stopLoss?: number;
  takeProfit?: number;
  executionStatus: 'PENDING' | 'EXECUTED' | 'FAILED' | 'SKIPPED';
  orderId?: string;
  executedAt?: Date;
  reason?: string;
}

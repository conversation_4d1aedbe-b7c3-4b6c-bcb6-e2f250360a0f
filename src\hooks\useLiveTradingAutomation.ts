
import { useConnectionStatus } from './useConnectionStatus';
import { useExecutionManager } from './useExecutionManager';
import { useBrokerManager } from './useBrokerManager';

export const useLiveTradingAutomation = () => {
  const { isConnected, connectionMessage, brokerAccount } = useConnectionStatus();
  
  const {
    executionConfig,
    executions,
    isAutoTrading,
    toggleAutoTrading,
    executeSignalManually,
    updateExecutionConfig,
    getExecutionStats
  } = useExecutionManager();

  const {
    broker,
    getCurrentPositions,
    getCurrentOrders,
    cancelOrder,
    closePosition
  } = useBrokerManager();

  return {
    // Connection status
    isConnected,
    isAutoTrading,
    connectionMessage,
    
    // Account data
    brokerAccount,
    
    // Execution data
    executions,
    executionConfig,
    
    // Actions
    toggleAutoTrading,
    executeSignalManually,
    updateExecutionConfig,
    getCurrentPositions,
    getCurrentOrders,
    cancelOrder,
    closePosition,
    getExecutionStats,
    
    // Broker instance
    broker
  };
};

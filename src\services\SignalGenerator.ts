import { MarketSignal, TechnicalIndicator, SignalGenerationConfig, OHLCV } from '@/types/signals';
import { TechnicalAnalysis } from './TechnicalAnalysis';

export class SignalGenerator {
  private config: SignalGenerationConfig;
  private signalHistory: Map<string, MarketSignal[]> = new Map();

  constructor(config: SignalGenerationConfig) {
    this.config = config;
  }

  /**
   * Generate comprehensive trading signals for a symbol
   */
  async generateSignals(
    symbol: string,
    ohlcvData: OHLCV[],
    timeframe: '1m' | '5m' | '15m' | '1h' | '4h' | '1d' = '15m'
  ): Promise<MarketSignal[]> {
    try {
      const signals: MarketSignal[] = [];
      const prices = ohlcvData.map(d => d.close);
      const currentPrice = prices[prices.length - 1];

      // Generate technical analysis signals
      const indicators = await this.generateTechnicalIndicators(ohlcvData);
      
      // Calculate overall signal strength and direction
      const aggregatedSignal = this.aggregateSignals(indicators);
      
      if (aggregatedSignal.confidence >= this.config.minConfidence) {
        const signal: MarketSignal = {
          id: `${symbol}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          symbol,
          type: 'TECHNICAL',
          signal: aggregatedSignal.signal,
          confidence: aggregatedSignal.confidence,
          strength: aggregatedSignal.strength,
          price: currentPrice,
          timeframe,
          indicators,
          reasoning: this.generateReasoning(indicators, aggregatedSignal),
          timestamp: new Date(),
          expiresAt: new Date(Date.now() + this.config.signalExpiryMinutes * 60 * 1000)
        };

        // Add price targets based on technical analysis
        this.addPriceTargets(signal, ohlcvData);
        
        signals.push(signal);
      }

      // Store in history for performance tracking
      this.updateSignalHistory(symbol, signals);

      return signals;
    } catch (error) {
      console.error(`Error generating signals for ${symbol}:`, error);
      return [];
    }
  }

  /**
   * Generate all technical indicators
   */
  private async generateTechnicalIndicators(ohlcvData: OHLCV[]): Promise<TechnicalIndicator[]> {
    const indicators: TechnicalIndicator[] = [];
    const prices = ohlcvData.map(d => d.close);

    try {
      // RSI
      if (this.config.enabledIndicators.includes('RSI')) {
        indicators.push(TechnicalAnalysis.calculateRSI(prices));
      }

      // MACD
      if (this.config.enabledIndicators.includes('MACD')) {
        indicators.push(TechnicalAnalysis.calculateMACD(prices));
      }

      // Bollinger Bands
      if (this.config.enabledIndicators.includes('BOLLINGER')) {
        indicators.push(TechnicalAnalysis.calculateBollingerBands(prices));
      }

      // Volume Analysis
      if (this.config.enabledIndicators.includes('VOLUME')) {
        indicators.push(TechnicalAnalysis.analyzeVolume(ohlcvData));
      }

      // Moving Average Crossover
      if (this.config.enabledIndicators.includes('MA_CROSS')) {
        indicators.push(this.calculateMovingAverageCrossover(prices));
      }

    } catch (error) {
      console.error('Error calculating technical indicators:', error);
    }

    return indicators;
  }

  /**
   * Calculate Moving Average Crossover signal
   */
  private calculateMovingAverageCrossover(prices: number[]): TechnicalIndicator {
    const shortMA = this.calculateSMA(prices.slice(-10), 10);
    const longMA = this.calculateSMA(prices.slice(-20), 20);
    const prevShortMA = this.calculateSMA(prices.slice(-11, -1), 10);
    const prevLongMA = this.calculateSMA(prices.slice(-21, -1), 20);

    let signal: 'BUY' | 'SELL' | 'HOLD' = 'HOLD';
    let strength = 50;

    // Golden Cross (bullish)
    if (shortMA > longMA && prevShortMA <= prevLongMA) {
      signal = 'BUY';
      strength = 75;
    }
    // Death Cross (bearish)
    else if (shortMA < longMA && prevShortMA >= prevLongMA) {
      signal = 'SELL';
      strength = 75;
    }

    return {
      name: 'MA Crossover',
      value: shortMA - longMA,
      signal,
      strength,
      timestamp: new Date()
    };
  }

  /**
   * Calculate Simple Moving Average
   */
  private calculateSMA(prices: number[], period: number): number {
    return prices.slice(-period).reduce((sum, price) => sum + price, 0) / period;
  }

  /**
   * Aggregate multiple signals into one overall signal
   */
  private aggregateSignals(indicators: TechnicalIndicator[]): {
    signal: 'BUY' | 'SELL' | 'HOLD';
    confidence: number;
    strength: number;
  } {
    if (indicators.length === 0) {
      return { signal: 'HOLD', confidence: 0, strength: 0 };
    }

    let buyScore = 0;
    let sellScore = 0;
    let totalWeight = 0;

    indicators.forEach(indicator => {
      const weight = indicator.strength / 100;
      totalWeight += weight;

      if (indicator.signal === 'BUY') {
        buyScore += weight;
      } else if (indicator.signal === 'SELL') {
        sellScore += weight;
      }
    });

    const buyPercentage = (buyScore / totalWeight) * 100;
    const sellPercentage = (sellScore / totalWeight) * 100;
    const holdPercentage = 100 - buyPercentage - sellPercentage;

    let signal: 'BUY' | 'SELL' | 'HOLD' = 'HOLD';
    let confidence = 0;
    let strength = 0;

    if (buyPercentage > sellPercentage && buyPercentage > holdPercentage) {
      signal = 'BUY';
      confidence = Math.min(95, buyPercentage);
      strength = Math.min(95, buyPercentage * 1.2);
    } else if (sellPercentage > buyPercentage && sellPercentage > holdPercentage) {
      signal = 'SELL';
      confidence = Math.min(95, sellPercentage);
      strength = Math.min(95, sellPercentage * 1.2);
    } else {
      signal = 'HOLD';
      confidence = Math.max(holdPercentage, 30);
      strength = 40;
    }

    return { signal, confidence, strength };
  }

  /**
   * Generate human-readable reasoning for the signal
   */
  private generateReasoning(indicators: TechnicalIndicator[], aggregatedSignal: any): string {
    const reasons: string[] = [];

    indicators.forEach(indicator => {
      if (indicator.signal !== 'HOLD' && indicator.strength > 60) {
        switch (indicator.name) {
          case 'RSI':
            if (indicator.signal === 'BUY') {
              reasons.push(`RSI oversold at ${indicator.value.toFixed(2)}`);
            } else {
              reasons.push(`RSI overbought at ${indicator.value.toFixed(2)}`);
            }
            break;
          case 'MACD':
            reasons.push(`MACD showing ${indicator.signal.toLowerCase()}ish momentum`);
            break;
          case 'Bollinger Bands':
            if (indicator.signal === 'BUY') {
              reasons.push('Price touching lower Bollinger Band');
            } else {
              reasons.push('Price touching upper Bollinger Band');
            }
            break;
          case 'Volume Analysis':
            reasons.push(`High volume confirming ${indicator.signal.toLowerCase()} pressure`);
            break;
          case 'MA Crossover':
            if (indicator.signal === 'BUY') {
              reasons.push('Golden cross detected');
            } else {
              reasons.push('Death cross detected');
            }
            break;
        }
      }
    });

    if (reasons.length === 0) {
      return `${aggregatedSignal.signal} signal with ${aggregatedSignal.confidence.toFixed(1)}% confidence`;
    }

    return reasons.join(', ') + ` (${aggregatedSignal.confidence.toFixed(1)}% confidence)`;
  }

  /**
   * Add price targets and stop loss based on technical analysis
   */
  private addPriceTargets(signal: MarketSignal, ohlcvData: OHLCV[]): void {
    try {
      const supportResistance = TechnicalAnalysis.findSupportResistance(ohlcvData);
      const currentPrice = signal.price;

      if (signal.signal === 'BUY') {
        signal.targetPrice = supportResistance.resistance;
        signal.stopLoss = Math.max(
          supportResistance.support,
          currentPrice * (1 - (1 / this.config.riskRewardRatio) * 0.01)
        );
      } else if (signal.signal === 'SELL') {
        signal.targetPrice = supportResistance.support;
        signal.stopLoss = Math.min(
          supportResistance.resistance,
          currentPrice * (1 + (1 / this.config.riskRewardRatio) * 0.01)
        );
      }
    } catch (error) {
      console.error('Error calculating price targets:', error);
    }
  }

  /**
   * Update signal history for performance tracking
   */
  private updateSignalHistory(symbol: string, signals: MarketSignal[]): void {
    if (!this.signalHistory.has(symbol)) {
      this.signalHistory.set(symbol, []);
    }

    const history = this.signalHistory.get(symbol)!;
    history.push(...signals);

    // Keep only recent signals (last 1000)
    if (history.length > 1000) {
      this.signalHistory.set(symbol, history.slice(-1000));
    }
  }

  /**
   * Get signal performance metrics
   */
  getSignalPerformance(symbol: string): any {
    const history = this.signalHistory.get(symbol) || [];
    if (history.length === 0) return null;

    const recentSignals = history.filter(s => 
      s.timestamp.getTime() > Date.now() - (30 * 24 * 60 * 60 * 1000) // Last 30 days
    );

    return {
      totalSignals: recentSignals.length,
      buySignals: recentSignals.filter(s => s.signal === 'BUY').length,
      sellSignals: recentSignals.filter(s => s.signal === 'SELL').length,
      avgConfidence: recentSignals.reduce((sum, s) => sum + s.confidence, 0) / recentSignals.length,
      avgStrength: recentSignals.reduce((sum, s) => sum + s.strength, 0) / recentSignals.length
    };
  }
}

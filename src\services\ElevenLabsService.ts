interface ElevenLabsConfig {
  apiKey: string;
  voiceId?: string;
  model?: string;
}

interface VoiceSettings {
  stability: number;
  similarity_boost: number;
  style?: number;
  use_speaker_boost?: boolean;
}

export class ElevenLabsService {
  private static apiKey: string | null = null;
  private static defaultVoiceId = 'EXAVITQu4vr4xnSDxMaL'; // Sarah - professional female voice
  private static defaultModel = 'eleven_turbo_v2_5'; // High quality, low latency

  /**
   * Initialize ElevenLabs service with API key
   */
  static initialize(config: ElevenLabsConfig): void {
    this.apiKey = config.apiKey;
    if (config.voiceId) this.defaultVoiceId = config.voiceId;
    if (config.model) this.defaultModel = config.model;
    
    // Store in localStorage for persistence
    localStorage.setItem('elevenlabs_api_key', config.apiKey);
    localStorage.setItem('elevenlabs_voice_id', this.defaultVoiceId);
    localStorage.setItem('elevenlabs_model', this.defaultModel);
  }

  /**
   * Get stored API key
   */
  private static getApiKey(): string {
    if (this.apiKey) return this.apiKey;
    
    const stored = localStorage.getItem('elevenlabs_api_key');
    if (stored) {
      this.apiKey = stored;
      return stored;
    }
    
    throw new Error('ElevenLabs API key not configured');
  }

  /**
   * Convert text to speech using ElevenLabs API
   */
  static async textToSpeech(
    text: string, 
    options?: {
      voiceId?: string;
      model?: string;
      voiceSettings?: VoiceSettings;
    }
  ): Promise<ArrayBuffer> {
    try {
      const apiKey = this.getApiKey();
      const voiceId = options?.voiceId || this.defaultVoiceId;
      const model = options?.model || this.defaultModel;
      
      const voiceSettings: VoiceSettings = {
        stability: 0.5,
        similarity_boost: 0.5,
        style: 0.0,
        use_speaker_boost: true,
        ...options?.voiceSettings
      };

      const response = await fetch(`https://api.elevenlabs.io/v1/text-to-speech/${voiceId}`, {
        method: 'POST',
        headers: {
          'Accept': 'audio/mpeg',
          'Content-Type': 'application/json',
          'xi-api-key': apiKey
        },
        body: JSON.stringify({
          text,
          model_id: model,
          voice_settings: voiceSettings
        })
      });

      if (!response.ok) {
        throw new Error(`ElevenLabs API error: ${response.statusText}`);
      }

      return await response.arrayBuffer();
    } catch (error) {
      console.error('ElevenLabs TTS error:', error);
      throw error;
    }
  }

  /**
   * Get available voices
   */
  static async getVoices(): Promise<any[]> {
    try {
      const apiKey = this.getApiKey();
      
      const response = await fetch('https://api.elevenlabs.io/v1/voices', {
        headers: {
          'xi-api-key': apiKey
        }
      });

      if (!response.ok) {
        throw new Error(`ElevenLabs API error: ${response.statusText}`);
      }

      const data = await response.json();
      return data.voices;
    } catch (error) {
      console.error('Failed to get voices:', error);
      return [];
    }
  }

  /**
   * Play audio buffer
   */
  static async playAudio(audioBuffer: ArrayBuffer): Promise<void> {
    return new Promise((resolve, reject) => {
      const blob = new Blob([audioBuffer], { type: 'audio/mpeg' });
      const audio = new Audio(URL.createObjectURL(blob));
      
      audio.onended = () => {
        URL.revokeObjectURL(audio.src);
        resolve();
      };
      
      audio.onerror = () => {
        URL.revokeObjectURL(audio.src);
        reject(new Error('Audio playback failed'));
      };
      
      audio.play().catch(reject);
    });
  }

  /**
   * Get current configuration
   */
  static getConfig(): { voiceId: string; model: string; hasApiKey: boolean } {
    return {
      voiceId: localStorage.getItem('elevenlabs_voice_id') || this.defaultVoiceId,
      model: localStorage.getItem('elevenlabs_model') || this.defaultModel,
      hasApiKey: !!localStorage.getItem('elevenlabs_api_key')
    };
  }

  /**
   * Check if service is configured
   */
  static isConfigured(): boolean {
    try {
      this.getApiKey();
      return true;
    } catch {
      return false;
    }
  }
}
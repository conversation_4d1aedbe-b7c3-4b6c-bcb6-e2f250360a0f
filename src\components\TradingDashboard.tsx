import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area } from 'recharts';
import { TrendingUp, TrendingDown, Brain, Shield, Target, DollarSign, BarChart3, Bot } from 'lucide-react';
import { Link } from 'react-router-dom';
import RiskManagementPanel from './RiskManagementPanel';
import SignalPanel from './SignalPanel';
import AIInsightsPanel from './AIInsightsPanel';
import TradingAutomationPanel from './TradingAutomationPanel';
import RealTimeChart from './RealTimeChart';
import { GoogleCloudDashboard } from './GoogleCloudDashboard';

interface MarketData {
  time: string;
  price: number;
  volume: number;
  signal: 'BUY' | 'SELL' | 'HOLD';
}

interface AISignal {
  symbol: string;
  signal: 'BUY' | 'SELL' | 'HOLD';
  confidence: number;
  reason: string;
  timestamp: string;
}

const TradingDashboard = () => {
  const [marketData, setMarketData] = useState<MarketData[]>([]);
  const [aiSignals, setAISignals] = useState<AISignal[]>([]);
  const [portfolio, setPortfolio] = useState({
    totalValue: 250000,
    dayChange: 12500,
    dayChangePercent: 5.26,
    positions: 12,
    availableCash: 48500
  });

  // Simulate real-time market data
  useEffect(() => {
    const generateMockData = () => {
      const now = new Date();
      const data: MarketData[] = [];
      let basePrice = 15420;
      
      for (let i = 30; i >= 0; i--) {
        const time = new Date(now.getTime() - i * 60000);
        basePrice += (Math.random() - 0.5) * 100;
        data.push({
          time: time.toLocaleTimeString(),
          price: Math.round(basePrice),
          volume: Math.floor(Math.random() * 1000000),
          signal: ['BUY', 'SELL', 'HOLD'][Math.floor(Math.random() * 3)] as 'BUY' | 'SELL' | 'HOLD'
        });
      }
      setMarketData(data);
    };

    const generateAISignals = () => {
      const signals: AISignal[] = [
        {
          symbol: 'NIFTY50',
          signal: 'BUY',
          confidence: 87,
          reason: 'Strong bullish momentum with RSI oversold',
          timestamp: new Date().toLocaleTimeString()
        },
        {
          symbol: 'BANKNIFTY',
          signal: 'HOLD',
          confidence: 72,
          reason: 'Consolidation phase, wait for breakout',
          timestamp: new Date().toLocaleTimeString()
        },
        {
          symbol: 'RELIANCE',
          signal: 'SELL',
          confidence: 91,
          reason: 'Bearish divergence detected in MACD',
          timestamp: new Date().toLocaleTimeString()
        }
      ];
      setAISignals(signals);
    };

    generateMockData();
    generateAISignals();

    const interval = setInterval(() => {
      generateMockData();
      if (Math.random() > 0.7) generateAISignals();
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const getSignalColor = (signal: string) => {
    switch (signal) {
      case 'BUY': return 'bg-green-500';
      case 'SELL': return 'bg-red-500';
      default: return 'bg-yellow-500';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 text-white p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
              Alpha Trade Flow AI
            </h1>
            <p className="text-slate-400 mt-1">Advanced AI-Powered Trading Platform</p>
          </div>
          <div className="flex items-center gap-4">
            <Badge variant="outline" className="bg-green-900/20 text-green-400 border-green-500">
              <div className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
              Live Trading
            </Badge>
            <Link to="/ai-workflow">
              <Button variant="outline" className="border-purple-500 text-purple-400 hover:bg-purple-500/10">
                <Bot className="w-4 h-4 mr-2" />
                AI Workflow Hub
              </Button>
            </Link>
            <Button variant="outline" className="border-blue-500 text-blue-400 hover:bg-blue-500/10" onClick={() => {
              const cloudSection = document.getElementById('google-cloud-section');
              cloudSection?.scrollIntoView({ behavior: 'smooth' });
            }}>
              <Bot className="w-4 h-4 mr-2" />
              Google Cloud AI
            </Button>
            <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
              <Target className="w-4 h-4 mr-2" />
              New Trade
            </Button>
          </div>
        </div>

        {/* Portfolio Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm">Portfolio Value</p>
                  <p className="text-2xl font-bold">₹{portfolio.totalValue.toLocaleString()}</p>
                </div>
                <DollarSign className="w-8 h-8 text-blue-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm">Day P&L</p>
                  <p className="text-2xl font-bold text-green-400">
                    +₹{portfolio.dayChange.toLocaleString()}
                  </p>
                  <p className="text-sm text-green-400">+{portfolio.dayChangePercent}%</p>
                </div>
                <TrendingUp className="w-8 h-8 text-green-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm">Active Positions</p>
                  <p className="text-2xl font-bold">{portfolio.positions}</p>
                </div>
                <Target className="w-8 h-8 text-purple-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm">Available Cash</p>
                  <p className="text-2xl font-bold">₹{portfolio.availableCash.toLocaleString()}</p>
                </div>
                <Shield className="w-8 h-8 text-orange-400" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Trading Interface */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Real-Time Chart */}
          <div className="lg:col-span-2">
            <RealTimeChart 
              symbols={['RELIANCE', 'TCS', 'HDFCBANK', 'INFY']}
              selectedSymbol="RELIANCE"
            />
          </div>

          {/* AI Signals Panel */}
          <SignalPanel />
        </div>

        {/* Trading Automation Panel - NEW */}
        <TradingAutomationPanel />

        {/* AI Insights Panel */}
        <AIInsightsPanel />

        {/* Risk Management Panel */}
        <RiskManagementPanel />

        {/* Google Cloud AI Integration */}
        <div id="google-cloud-section">
          <GoogleCloudDashboard />
        </div>

        {/* Signal Generation Panel */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="w-5 h-5 text-green-400" />
                Technical Analysis Overview
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-slate-300">Market Indicators</h4>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span className="text-slate-400">RSI (14):</span>
                        <span className="text-yellow-400">45.2</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-400">MACD:</span>
                        <span className="text-green-400">+12.4</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-400">BB Position:</span>
                        <span className="text-blue-400">Mid</span>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-slate-300">Volume Profile</h4>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span className="text-slate-400">Vol Ratio:</span>
                        <span className="text-green-400">1.8x</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-400">Trend:</span>
                        <span className="text-green-400">Bullish</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-400">Momentum:</span>
                        <span className="text-yellow-400">Moderate</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="w-5 h-5 text-orange-400" />
                Signal Configuration
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm text-slate-400">Min Confidence</label>
                    <div className="text-lg font-medium text-white">60%</div>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm text-slate-400">Risk:Reward</label>
                    <div className="text-lg font-medium text-white">1:2</div>
                  </div>
                </div>
                <div className="space-y-2">
                  <label className="text-sm text-slate-400">Active Indicators</label>
                  <div className="flex flex-wrap gap-1">
                    {['RSI', 'MACD', 'BB', 'Volume', 'MA'].map((indicator) => (
                      <Badge
                        key={indicator}
                        variant="outline"
                        className="text-xs bg-blue-900/20 text-blue-400 border-blue-500"
                      >
                        {indicator}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default TradingDashboard;

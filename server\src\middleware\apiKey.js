// Simple API key middleware for securing selected endpoints
// Behavior:
// - If BACKEND_API_KEY is unset, allow all requests (dev-friendly)
// - If BACKEND_API_KEY is set, require header X-API-Key to match

module.exports = function apiKey(req, res, next) {
  const requiredKey = process.env.BACKEND_API_KEY;
  if (!requiredKey) {
    return next(); // no key configured => allow
  }
  const provided = req.header('X-API-Key');
  if (provided && provided === requiredKey) {
    return next();
  }
  return res.status(401).json({ error: 'Unauthorized: missing or invalid API key' });
};


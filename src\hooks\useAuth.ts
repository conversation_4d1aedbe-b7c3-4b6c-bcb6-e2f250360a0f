
import { useState, useEffect, useCallback } from 'react';
import { secureStorage } from '@/services/SecureStorage';
import { validateSession } from '@/lib/security';

interface User {
  email: string;
  name: string;
  sessionToken?: string;
}

export const useAuth = () => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  const loadUserSession = useCallback(async () => {
    try {
      const sessionData = await secureStorage.getSecureItem('user_session');
      if (sessionData) {
        const userData = JSON.parse(sessionData);
        if (userData.sessionToken && validateSession(userData.sessionToken)) {
          setUser(userData);
          setIsAuthenticated(true);
        } else {
          // Session expired, clear it
          await secureStorage.removeSecureItem('user_session');
        }
      }
    } catch (error) {
      console.error('Failed to load user session:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const login = useCallback(async (userData: User) => {
    try {
      await secureStorage.setSecureItem('user_session', JSON.stringify(userData), true, 24 * 60 * 60 * 1000);
      setUser(userData);
      setIsAuthenticated(true);
    } catch (error) {
      console.error('Failed to save user session:', error);
      throw error;
    }
  }, []);

  const logout = useCallback(async () => {
    try {
      // Clear user session
      await secureStorage.removeSecureItem('user_session');
      
      // Clear session token if exists
      if (user?.sessionToken) {
        await secureStorage.removeSecureItem(`session_${user.sessionToken}`);
      }
      
      setUser(null);
      setIsAuthenticated(false);
    } catch (error) {
      console.error('Failed to logout:', error);
    }
  }, [user]);

  const refreshSession = useCallback(async () => {
    if (user?.sessionToken) {
      const isValid = validateSession(user.sessionToken);
      if (!isValid) {
        await logout();
      }
    }
  }, [user, logout]);

  useEffect(() => {
    loadUserSession();
  }, [loadUserSession]);

  // Refresh session every 5 minutes
  useEffect(() => {
    if (isAuthenticated) {
      const interval = setInterval(refreshSession, 5 * 60 * 1000);
      return () => clearInterval(interval);
    }
  }, [isAuthenticated, refreshSession]);

  return {
    user,
    isLoading,
    isAuthenticated,
    login,
    logout,
    refreshSession
  };
};

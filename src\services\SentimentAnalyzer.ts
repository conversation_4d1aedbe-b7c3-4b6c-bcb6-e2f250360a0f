
import { NewsArticle, SocialMediaPost, SentimentScore } from '@/types/ai';
import { GoogleCloudService } from './GoogleCloudService';

export class SentimentAnalyzer {
  private static readonly SENTIMENT_KEYWORDS = {
    positive: [
      'bullish', 'buy', 'moon', 'surge', 'breakout', 'rally', 'green', 'profit',
      'gain', 'upward', 'strong', 'growth', 'momentum', 'support', 'target',
      'bullish', 'optimistic', 'positive', 'excellent', 'outstanding'
    ],
    negative: [
      'bearish', 'sell', 'crash', 'dump', 'breakdown', 'fall', 'red', 'loss',
      'drop', 'downward', 'weak', 'decline', 'resistance', 'stop',
      'bearish', 'pessimistic', 'negative', 'terrible', 'awful'
    ],
    neutral: [
      'hold', 'wait', 'consolidation', 'sideways', 'range', 'neutral'
    ]
  };

  /**
   * Analyze sentiment of news articles
   */
  static async analyzeNews(articles: NewsArticle[]): Promise<SentimentScore> {
    if (articles.length === 0) {
      return { polarity: 0, confidence: 0, label: 'NEUTRAL', subjectivity: 0 };
    }

    let totalPolarity = 0;
    let totalConfidence = 0;
    let totalSubjectivity = 0;

    // Process articles sequentially to avoid API rate limits
    for (const article of articles) {
      const sentiment = await this.analyzeSentiment(article.title + ' ' + article.content);
      totalPolarity += sentiment.polarity;
      totalConfidence += sentiment.confidence;
      totalSubjectivity += sentiment.subjectivity;
    }

    const avgPolarity = totalPolarity / articles.length;
    const avgConfidence = totalConfidence / articles.length;
    const avgSubjectivity = totalSubjectivity / articles.length;

    return {
      polarity: avgPolarity,
      confidence: avgConfidence,
      label: this.getLabel(avgPolarity),
      subjectivity: avgSubjectivity
    };
  }

  /**
   * Analyze sentiment of social media posts
   */
  static async analyzeSocialMedia(posts: SocialMediaPost[]): Promise<SentimentScore> {
    if (posts.length === 0) {
      return { polarity: 0, confidence: 0, label: 'NEUTRAL', subjectivity: 0 };
    }

    // Weight posts by engagement
    let weightedPolarity = 0;
    let weightedConfidence = 0;
    let weightedSubjectivity = 0;
    let totalWeight = 0;

    // Process posts sequentially to avoid API rate limits
    for (const post of posts) {
      const sentiment = await this.analyzeSentiment(post.content);
      const weight = this.calculateEngagementWeight(post.engagement);
      
      weightedPolarity += sentiment.polarity * weight;
      weightedConfidence += sentiment.confidence * weight;
      weightedSubjectivity += sentiment.subjectivity * weight;
      totalWeight += weight;
    }

    if (totalWeight === 0) {
      return { polarity: 0, confidence: 0, label: 'NEUTRAL', subjectivity: 0 };
    }

    const avgPolarity = weightedPolarity / totalWeight;
    const avgConfidence = weightedConfidence / totalWeight;
    const avgSubjectivity = weightedSubjectivity / totalWeight;

    return {
      polarity: avgPolarity,
      confidence: avgConfidence,
      label: this.getLabel(avgPolarity),
      subjectivity: avgSubjectivity
    };
  }

  /**
   * Enhanced sentiment analysis using Google Cloud or fallback to keyword matching
   */
  private static async analyzeSentiment(text: string): Promise<SentimentScore> {
    try {
      // Try Google Cloud Natural Language API first
      return await GoogleCloudService.analyzeSentiment(text);
    } catch (error) {
      console.log('Using fallback sentiment analysis:', error);
      return this.fallbackSentimentAnalysis(text);
    }
  }

  /**
   * Fallback sentiment analysis using keyword matching
   */
  private static fallbackSentimentAnalysis(text: string): SentimentScore {
    const words = text.toLowerCase().split(/\s+/);
    
    let positiveCount = 0;
    let negativeCount = 0;
    let neutralCount = 0;
    let subjectiveCount = 0;

    words.forEach(word => {
      if (this.SENTIMENT_KEYWORDS.positive.includes(word)) {
        positiveCount++;
        subjectiveCount++;
      } else if (this.SENTIMENT_KEYWORDS.negative.includes(word)) {
        negativeCount++;
        subjectiveCount++;
      } else if (this.SENTIMENT_KEYWORDS.neutral.includes(word)) {
        neutralCount++;
      }
    });

    const totalSentimentWords = positiveCount + negativeCount + neutralCount;
    const polarity = totalSentimentWords > 0 
      ? (positiveCount - negativeCount) / totalSentimentWords
      : 0;

    const confidence = totalSentimentWords > 0 
      ? Math.min(1, totalSentimentWords / 10)
      : 0;

    const subjectivity = words.length > 0 
      ? subjectiveCount / words.length
      : 0;

    return {
      polarity: Math.max(-1, Math.min(1, polarity)),
      confidence: Math.max(0, Math.min(1, confidence)),
      label: this.getLabel(polarity),
      subjectivity: Math.max(0, Math.min(1, subjectivity))
    };
  }

  private static getLabel(polarity: number): 'POSITIVE' | 'NEGATIVE' | 'NEUTRAL' {
    if (polarity > 0.1) return 'POSITIVE';
    if (polarity < -0.1) return 'NEGATIVE';
    return 'NEUTRAL';
  }

  private static calculateEngagementWeight(engagement: { likes: number; shares: number; comments: number }): number {
    const total = engagement.likes + engagement.shares * 2 + engagement.comments * 1.5;
    return Math.log(total + 1) / Math.log(100); // Normalize to 0-1 range
  }

  /**
   * Extract mentioned symbols from text
   */
  static extractSymbols(text: string): string[] {
    const symbols = new Set<string>();
    
    // Common Indian stock symbols
    const stockSymbols = [
      'NIFTY', 'SENSEX', 'RELIANCE', 'TCS', 'INFY', 'HDFC', 'ICICI', 'SBI',
      'BAJFINANCE', 'BHARTIARTL', 'HDFCBANK', 'KOTAKBANK', 'LT', 'ASIANPAINT',
      'MARUTI', 'TITAN', 'NESTLEIND', 'HCLTECH', 'WIPRO', 'TECHM'
    ];

    const upperText = text.toUpperCase();
    stockSymbols.forEach(symbol => {
      if (upperText.includes(symbol)) {
        symbols.add(symbol);
      }
    });

    return Array.from(symbols);
  }
}

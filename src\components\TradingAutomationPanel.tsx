
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  Bot, 
  Play, 
  Pause, 
  Settings, 
  TrendingUp, 
  TrendingDown,
  Clock,
  Target,
  AlertTriangle,
  CheckCircle,
  DollarSign,
  Activity
} from 'lucide-react';
import { useTradingAutomation } from '@/hooks/useTradingAutomation';
import { useSignalGeneration } from '@/hooks/useSignalGeneration';
import { TradeExecution } from '@/types/broker';
import { MarketSignal } from '@/types/signals';

const TradingAutomationPanel = () => {
  const {
    isConnected,
    isAutoTrading,
    brokerAccount,
    executions,
    executionConfig,
    toggleAutoTrading,
    executeSignalManually,
    updateExecutionConfig,
    getCurrentOrders,
    getCurrentPositions,
    getExecutionStats
  } = useTradingAutomation();

  const { signals } = useSignalGeneration();
  const [currentOrders, setCurrentOrders] = useState<any[]>([]);
  const [currentPositions, setCurrentPositions] = useState<any[]>([]);
  const [executionStats, setExecutionStats] = useState<any>(null);

  // Refresh data periodically
  useEffect(() => {
    const refreshData = async () => {
      if (isConnected) {
        try {
          const [orders, positions, stats] = await Promise.all([
            getCurrentOrders(),
            getCurrentPositions(),
            getExecutionStats()
          ]);
          
          setCurrentOrders(orders);
          setCurrentPositions(positions);
          setExecutionStats(stats);
        } catch (error) {
          console.error('Error refreshing automation data:', error);
        }
      }
    };

    refreshData();
    const interval = setInterval(refreshData, 5000);
    return () => clearInterval(interval);
  }, [isConnected, getCurrentOrders, getCurrentPositions, getExecutionStats]);

  const getExecutionStatusColor = (status: TradeExecution['executionStatus']) => {
    switch (status) {
      case 'EXECUTED':
        return 'bg-green-900/20 text-green-400 border-green-500';
      case 'FAILED':
        return 'bg-red-900/20 text-red-400 border-red-500';
      case 'SKIPPED':
        return 'bg-yellow-900/20 text-yellow-400 border-yellow-500';
      case 'PENDING':
        return 'bg-blue-900/20 text-blue-400 border-blue-500';
      default:
        return 'bg-slate-900/20 text-slate-400 border-slate-500';
    }
  };

  const getExecutionStatusIcon = (status: TradeExecution['executionStatus']) => {
    switch (status) {
      case 'EXECUTED':
        return <CheckCircle className="w-3 h-3" />;
      case 'FAILED':
        return <AlertTriangle className="w-3 h-3" />;
      case 'SKIPPED':
        return <Clock className="w-3 h-3" />;
      case 'PENDING':
        return <Activity className="w-3 h-3" />;
      default:
        return <Clock className="w-3 h-3" />;
    }
  };

  const handleExecuteSignal = async (signal: MarketSignal) => {
    try {
      await executeSignalManually(signal);
    } catch (error) {
      console.error('Error executing signal:', error);
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-IN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  return (
    <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Bot className="w-5 h-5 text-blue-400" />
            Trading Automation
            <Badge className={isConnected ? 'bg-green-900/20 text-green-400 border-green-500' : 'bg-red-900/20 text-red-400 border-red-500'}>
              {isConnected ? 'Connected' : 'Disconnected'}
            </Badge>
          </div>
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <span className="text-sm text-slate-300">Auto Trading</span>
              <Switch
                checked={isAutoTrading}
                onCheckedChange={toggleAutoTrading}
                disabled={!isConnected}
              />
            </div>
            <Button
              size="sm"
              onClick={toggleAutoTrading}
              disabled={!isConnected}
              className={isAutoTrading ? 
                "bg-red-600 hover:bg-red-700" : 
                "bg-green-600 hover:bg-green-700"
              }
            >
              {isAutoTrading ? (
                <>
                  <Pause className="w-3 h-3 mr-1" />
                  Stop
                </>
              ) : (
                <>
                  <Play className="w-3 h-3 mr-1" />
                  Start
                </>
              )}
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="status" className="space-y-4">
          <TabsList className="grid w-full grid-cols-5 bg-slate-700/50">
            <TabsTrigger value="status">Status</TabsTrigger>
            <TabsTrigger value="executions">Executions</TabsTrigger>
            <TabsTrigger value="positions">Positions</TabsTrigger>
            <TabsTrigger value="orders">Orders</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="status" className="space-y-4">
            {/* Account Summary */}
            {brokerAccount && (
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center p-3 bg-slate-700/30 rounded-lg">
                  <div className="text-lg font-bold text-white">
                    ₹{brokerAccount.balance.toLocaleString()}
                  </div>
                  <div className="text-xs text-slate-400">Balance</div>
                </div>
                <div className="text-center p-3 bg-slate-700/30 rounded-lg">
                  <div className="text-lg font-bold text-green-400">
                    ₹{brokerAccount.unrealizedPnL.toLocaleString()}
                  </div>
                  <div className="text-xs text-slate-400">Unrealized P&L</div>
                </div>
                <div className="text-center p-3 bg-slate-700/30 rounded-lg">
                  <div className="text-lg font-bold text-blue-400">
                    {brokerAccount.positions.length}
                  </div>
                  <div className="text-xs text-slate-400">Positions</div>
                </div>
                <div className="text-center p-3 bg-slate-700/30 rounded-lg">
                  <div className="text-lg font-bold text-purple-400">
                    {brokerAccount.orders.filter(o => o.status === 'PENDING').length}
                  </div>
                  <div className="text-xs text-slate-400">Pending Orders</div>
                </div>
              </div>
            )}

            {/* Execution Statistics */}
            {executionStats && (
              <div className="space-y-3">
                <h4 className="text-sm font-medium text-slate-300">Execution Statistics</h4>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-sm">
                  <div className="flex justify-between">
                    <span className="text-slate-400">Total Signals:</span>
                    <span className="text-white">{executionStats.total}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-400">Executed:</span>
                    <span className="text-green-400">{executionStats.executed}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-400">Execution Rate:</span>
                    <span className="text-blue-400">{executionStats.executionRate.toFixed(1)}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-400">Avg Confidence:</span>
                    <span className="text-purple-400">{executionStats.avgConfidence.toFixed(1)}%</span>
                  </div>
                </div>
              </div>
            )}

            {/* Recent Signals for Manual Execution */}
            <div className="space-y-3">
              <h4 className="text-sm font-medium text-slate-300">Recent Signals (Manual Execution)</h4>
              <div className="space-y-2 max-h-48 overflow-y-auto">
                {signals
                  .filter(signal => signal.signal !== 'HOLD' && signal.confidence >= 60)
                  .slice(0, 5)
                  .map((signal) => (
                    <div
                      key={signal.id}
                      className="flex items-center justify-between p-2 bg-slate-700/30 rounded border border-slate-600"
                    >
                      <div className="flex items-center gap-2 flex-1">
                        <span className="font-medium text-white">{signal.symbol}</span>
                        <Badge className={signal.signal === 'BUY' ? 'bg-green-900/20 text-green-400 border-green-500' : 'bg-red-900/20 text-red-400 border-red-500'}>
                          {signal.signal === 'BUY' ? <TrendingUp className="w-3 h-3 mr-1" /> : <TrendingDown className="w-3 h-3 mr-1" />}
                          {signal.signal}
                        </Badge>
                        <span className="text-sm text-slate-400">{signal.confidence}%</span>
                      </div>
                      <Button
                        size="sm"
                        onClick={() => handleExecuteSignal(signal)}
                        disabled={!isConnected}
                        className="bg-blue-600 hover:bg-blue-700"
                      >
                        Execute
                      </Button>
                    </div>
                  ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="executions" className="space-y-3">
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {executions.length === 0 ? (
                <div className="text-center py-8 text-slate-400">
                  <Activity className="w-8 h-8 mx-auto mb-2 opacity-50" />
                  <p>No executions yet</p>
                </div>
              ) : (
                executions.map((execution) => (
                  <div
                    key={execution.signalId}
                    className="p-3 bg-slate-700/30 rounded-lg border border-slate-600"
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <span className="font-medium text-white">{execution.symbol}</span>
                        <Badge className={execution.signalType === 'BUY' ? 'bg-green-900/20 text-green-400 border-green-500' : 'bg-red-900/20 text-red-400 border-red-500'}>
                          {execution.signalType}
                        </Badge>
                        <Badge className={getExecutionStatusColor(execution.executionStatus)}>
                          {getExecutionStatusIcon(execution.executionStatus)}
                          {execution.executionStatus}
                        </Badge>
                      </div>
                      <div className="text-sm text-slate-400">
                        {execution.confidence}% confidence
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between text-sm mb-2">
                      <div className="text-slate-300">
                        Qty: {execution.suggestedQuantity} @ ₹{execution.suggestedPrice.toLocaleString()}
                      </div>
                      {execution.executedAt && (
                        <div className="text-slate-400">
                          {formatTime(execution.executedAt)}
                        </div>
                      )}
                    </div>

                    <p className="text-xs text-slate-400">{execution.reason}</p>
                  </div>
                ))
              )}
            </div>
          </TabsContent>

          <TabsContent value="positions" className="space-y-3">
            <div className="space-y-2">
              {currentPositions.length === 0 ? (
                <div className="text-center py-8 text-slate-400">
                  <Target className="w-8 h-8 mx-auto mb-2 opacity-50" />
                  <p>No open positions</p>
                </div>
              ) : (
                currentPositions.map((position, index) => (
                  <div
                    key={index}
                    className="p-3 bg-slate-700/30 rounded-lg border border-slate-600"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className="font-medium text-white">{position.symbol}</span>
                        <Badge className={position.side === 'LONG' ? 'bg-green-900/20 text-green-400 border-green-500' : 'bg-red-900/20 text-red-400 border-red-500'}>
                          {position.side}
                        </Badge>
                      </div>
                      <div className={`text-sm font-medium ${position.unrealizedPnL >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                        ₹{position.unrealizedPnL.toLocaleString()}
                      </div>
                    </div>
                    
                    <div className="mt-2 text-sm text-slate-300">
                      Qty: {Math.abs(position.quantity)} | Avg: ₹{position.averagePrice.toLocaleString()} | Current: ₹{position.currentPrice.toLocaleString()}
                    </div>
                  </div>
                ))
              )}
            </div>
          </TabsContent>

          <TabsContent value="orders" className="space-y-3">
            <div className="space-y-2">
              {currentOrders.length === 0 ? (
                <div className="text-center py-8 text-slate-400">
                  <Clock className="w-8 h-8 mx-auto mb-2 opacity-50" />
                  <p>No active orders</p>
                </div>
              ) : (
                currentOrders.map((order) => (
                  <div
                    key={order.id}
                    className="p-3 bg-slate-700/30 rounded-lg border border-slate-600"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className="font-medium text-white">{order.symbol}</span>
                        <Badge className={order.side === 'BUY' ? 'bg-green-900/20 text-green-400 border-green-500' : 'bg-red-900/20 text-red-400 border-red-500'}>
                          {order.side}
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          {order.type}
                        </Badge>
                      </div>
                      <Badge className={order.status === 'FILLED' ? 'bg-green-900/20 text-green-400 border-green-500' : 'bg-blue-900/20 text-blue-400 border-blue-500'}>
                        {order.status}
                      </Badge>
                    </div>
                    
                    <div className="mt-2 text-sm text-slate-300">
                      Qty: {order.quantity} | Price: ₹{order.price?.toLocaleString() || 'Market'}
                    </div>
                  </div>
                ))
              )}
            </div>
          </TabsContent>

          <TabsContent value="settings" className="space-y-4">
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm text-slate-300">Min Confidence (%)</label>
                  <div className="text-lg font-medium text-white">{executionConfig.minConfidenceThreshold}%</div>
                </div>
                <div className="space-y-2">
                  <label className="text-sm text-slate-300">Max Signals/Hour</label>
                  <div className="text-lg font-medium text-white">{executionConfig.maxSignalsPerHour}</div>
                </div>
                <div className="space-y-2">
                  <label className="text-sm text-slate-300">Default Risk %</label>
                  <div className="text-lg font-medium text-white">{executionConfig.defaultRiskPercentage}%</div>
                </div>
                <div className="space-y-2">
                  <label className="text-sm text-slate-300">Auto Execute</label>
                  <div className={`text-lg font-medium ${executionConfig.autoExecuteSignals ? 'text-green-400' : 'text-red-400'}`}>
                    {executionConfig.autoExecuteSignals ? 'Enabled' : 'Disabled'}
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-sm text-slate-300">Enabled Signal Types</label>
                <div className="flex flex-wrap gap-1">
                  {executionConfig.signalTypes.map((type) => (
                    <Badge
                      key={type}
                      variant="outline"
                      className="text-xs bg-blue-900/20 text-blue-400 border-blue-500"
                    >
                      {type}
                    </Badge>
                  ))}
                </div>
              </div>

              <div className="flex items-center gap-4 pt-2">
                <div className="flex items-center gap-2">
                  <span className="text-sm text-slate-300">Stop Loss</span>
                  <Badge className={executionConfig.enableStopLoss ? 'bg-green-900/20 text-green-400 border-green-500' : 'bg-red-900/20 text-red-400 border-red-500'}>
                    {executionConfig.enableStopLoss ? 'ON' : 'OFF'}
                  </Badge>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-sm text-slate-300">Take Profit</span>
                  <Badge className={executionConfig.enableTakeProfit ? 'bg-green-900/20 text-green-400 border-green-500' : 'bg-red-900/20 text-red-400 border-red-500'}>
                    {executionConfig.enableTakeProfit ? 'ON' : 'OFF'}
                  </Badge>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default TradingAutomationPanel;

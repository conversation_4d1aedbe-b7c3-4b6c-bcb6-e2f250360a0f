
import { useState, useEffect, useCallback } from 'react';
import { MarketSignal, SignalGenerationConfig, OHLCV } from '@/types/signals';
import { SignalGenerator } from '@/services/SignalGenerator';

const defaultConfig: SignalGenerationConfig = {
  enabledIndicators: ['RSI', 'MACD', 'BOLLINGER', 'VOLUME', 'MA_CROSS'],
  minConfidence: 60,
  maxSignalsPerSymbol: 5,
  signalExpiryMinutes: 30,
  riskRewardRatio: 2.0
};

// Mock market data generator for demonstration
const generateMockOHLCV = (symbol: string, count: number = 50): OHLCV[] => {
  const data: OHLCV[] = [];
  let basePrice = 15000 + Math.random() * 5000;
  
  for (let i = 0; i < count; i++) {
    const change = (Math.random() - 0.5) * 200;
    const open = basePrice;
    const close = open + change;
    const high = Math.max(open, close) + Math.random() * 50;
    const low = Math.min(open, close) - Math.random() * 50;
    const volume = Math.floor(Math.random() * 1000000) + 500000;
    
    data.push({
      open,
      high,
      low,
      close,
      volume,
      timestamp: new Date(Date.now() - (count - i) * 60000) // 1 minute intervals
    });
    
    basePrice = close;
  }
  
  return data;
};

export const useSignalGeneration = () => {
  const [signals, setSignals] = useState<MarketSignal[]>([]);
  const [config, setConfig] = useState<SignalGenerationConfig>(defaultConfig);
  const [generator, setGenerator] = useState<SignalGenerator | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);

  // Initialize signal generator
  useEffect(() => {
    const signalGenerator = new SignalGenerator(config);
    setGenerator(signalGenerator);
  }, [config]);

  /**
   * Generate signals for a specific symbol
   */
  const generateSignalsForSymbol = useCallback(async (
    symbol: string,
    timeframe: '1m' | '5m' | '15m' | '1h' | '4h' | '1d' = '15m'
  ) => {
    if (!generator) return [];

    setIsGenerating(true);
    try {
      // In a real implementation, you'd fetch actual market data
      const ohlcvData = generateMockOHLCV(symbol);
      
      const newSignals = await generator.generateSignals(symbol, ohlcvData, timeframe);
      
      // Update signals state
      setSignals(prevSignals => {
        // Remove old signals for this symbol
        const filteredSignals = prevSignals.filter(s => s.symbol !== symbol);
        // Add new signals
        return [...filteredSignals, ...newSignals];
      });

      setLastUpdate(new Date());
      console.log(`Generated ${newSignals.length} signals for ${symbol}:`, newSignals);
      
      return newSignals;
    } catch (error) {
      console.error(`Error generating signals for ${symbol}:`, error);
      return [];
    } finally {
      setIsGenerating(false);
    }
  }, [generator]);

  /**
   * Generate signals for multiple symbols
   */
  const generateSignalsForMultipleSymbols = useCallback(async (
    symbols: string[],
    timeframe: '1m' | '5m' | '15m' | '1h' | '4h' | '1d' = '15m'
  ) => {
    if (!generator) return [];

    setIsGenerating(true);
    const allSignals: MarketSignal[] = [];

    try {
      for (const symbol of symbols) {
        const symbolSignals = await generateSignalsForSymbol(symbol, timeframe);
        allSignals.push(...symbolSignals);
        
        // Small delay to prevent overwhelming the system
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      setLastUpdate(new Date());
      return allSignals;
    } catch (error) {
      console.error('Error generating signals for multiple symbols:', error);
      return [];
    } finally {
      setIsGenerating(false);
    }
  }, [generateSignalsForSymbol, generator]);

  /**
   * Clean up expired signals
   */
  const cleanupExpiredSignals = useCallback(() => {
    const now = new Date();
    setSignals(prevSignals => 
      prevSignals.filter(signal => 
        !signal.expiresAt || signal.expiresAt.getTime() > now.getTime()
      )
    );
  }, []);

  /**
   * Get signals for a specific symbol
   */
  const getSignalsForSymbol = useCallback((symbol: string) => {
    return signals.filter(signal => signal.symbol === symbol);
  }, [signals]);

  /**
   * Get signals by type
   */
  const getSignalsByType = useCallback((type: MarketSignal['type']) => {
    return signals.filter(signal => signal.type === type);
  }, [signals]);

  /**
   * Update signal generation configuration
   */
  const updateConfig = useCallback((newConfig: Partial<SignalGenerationConfig>) => {
    setConfig(prevConfig => ({ ...prevConfig, ...newConfig }));
  }, []);

  // Auto cleanup expired signals every minute
  useEffect(() => {
    const interval = setInterval(cleanupExpiredSignals, 60000);
    return () => clearInterval(interval);
  }, [cleanupExpiredSignals]);

  // Demo: Auto-generate signals for popular symbols
  useEffect(() => {
    const demoSymbols = ['NIFTY50', 'BANKNIFTY', 'RELIANCE', 'TCS', 'INFY'];
    
    const generateDemoSignals = async () => {
      await generateSignalsForMultipleSymbols(demoSymbols);
    };

    // Generate initial signals
    generateDemoSignals();

    // Generate new signals every 2 minutes for demo
    const interval = setInterval(generateDemoSignals, 120000);
    
    return () => clearInterval(interval);
  }, [generateSignalsForMultipleSymbols]);

  return {
    signals,
    config,
    generator,
    isGenerating,
    lastUpdate,
    generateSignalsForSymbol,
    generateSignalsForMultipleSymbols,
    getSignalsForSymbol,
    getSignalsByType,
    updateConfig,
    cleanupExpiredSignals
  };
};


import { useBrokerConnection } from './useBrokerConnection';
import { useBrokerOperations } from './useBrokerOperations';

export const useBrokerManager = () => {
  const { broker, isConnected, setBrokerAccount } = useBrokerConnection();

  const {
    getCurrentPositions,
    getCurrentOrders,
    cancelOrder,
    closePosition
  } = useBrokerOperations({
    broker,
    isConnected,
    setBrokerAccount
  });

  return {
    broker,
    getCurrentPositions,
    getCurrentOrders,
    cancelOrder,
    closePosition
  };
};

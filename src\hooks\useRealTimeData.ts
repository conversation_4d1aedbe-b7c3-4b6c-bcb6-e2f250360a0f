import { useState, useEffect, useCallback } from 'react';
import { realTimeDataStream, StreamData, NewsItem } from '@/services/RealTimeDataStream';

export const useRealTimeData = (symbols: string[]) => {
  const [data, setData] = useState<Record<string, StreamData>>({});
  const [isConnected, setIsConnected] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);

  useEffect(() => {
    const unsubscribers: (() => void)[] = [];

    const connectAndSubscribe = async () => {
      try {
        const connected = await realTimeDataStream.connect();
        setIsConnected(connected);
        
        if (connected) {
          setConnectionError(null);
          
          // Subscribe to each symbol
          symbols.forEach(symbol => {
            const unsubscribe = realTimeDataStream.subscribe(symbol, (streamData) => {
              setData(prev => ({
                ...prev,
                [symbol]: streamData
              }));
            });
            unsubscribers.push(unsubscribe);
          });
        }
      } catch (error) {
        console.error('Real-time data connection error:', error);
        setConnectionError(error instanceof Error ? error.message : 'Connection failed');
        setIsConnected(false);
      }
    };

    connectAndSubscribe();

    return () => {
      // Cleanup subscriptions
      unsubscribers.forEach(unsubscribe => unsubscribe());
    };
  }, [symbols.join(',')]); // Re-subscribe when symbols change

  const getLatestPrice = useCallback((symbol: string): number | null => {
    return data[symbol]?.price || null;
  }, [data]);

  const getChange = useCallback((symbol: string): { change: number; changePercent: number } | null => {
    const symbolData = data[symbol];
    if (!symbolData) return null;
    
    return {
      change: symbolData.change,
      changePercent: symbolData.changePercent
    };
  }, [data]);

  return {
    data,
    isConnected,
    connectionError,
    getLatestPrice,
    getChange,
    subscribedSymbols: symbols
  };
};

export const useRealTimeNews = () => {
  const [news, setNews] = useState<NewsItem[]>([]);
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    let unsubscribe: (() => void) | undefined;

    const connectAndSubscribe = async () => {
      try {
        const connected = await realTimeDataStream.connect();
        setIsConnected(connected);
        
        if (connected) {
          unsubscribe = realTimeDataStream.subscribeToNews((newsItem) => {
            setNews(prev => [newsItem, ...prev.slice(0, 49)]); // Keep last 50 items
          });
        }
      } catch (error) {
        console.error('Real-time news connection error:', error);
        setIsConnected(false);
      }
    };

    connectAndSubscribe();

    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, []);

  const getNewsBySymbol = useCallback((symbol: string): NewsItem[] => {
    return news.filter(item => item.symbols.includes(symbol));
  }, [news]);

  const getRecentNews = useCallback((count: number = 10): NewsItem[] => {
    return news.slice(0, count);
  }, [news]);

  return {
    news,
    isConnected,
    getNewsBySymbol,
    getRecentNews
  };
};

import { useState, useEffect, useCallback } from 'react';
import { MarketSignal } from '@/types/signals';
import { TradeExecution, BrokerAccount } from '@/types/broker';
import { PaperTradingBroker } from '@/services/PaperTradingBroker';
import { TradeExecutionEngine, ExecutionConfig } from '@/services/TradeExecutionEngine';
import { useRiskManagement } from './useRiskManagement';
import { useSignalGeneration } from './useSignalGeneration';

const defaultExecutionConfig: ExecutionConfig = {
  autoExecuteSignals: false, // Start with manual execution
  minConfidenceThreshold: 70,
  maxSignalsPerHour: 3,
  enableStopLoss: true,
  enableTakeProfit: true,
  defaultRiskPercentage: 2,
  signalTypes: ['TECHNICAL', 'AI_PREDICTION']
};

export const useTradingAutomation = () => {
  const { account, riskParams } = useRiskManagement();
  const { signals } = useSignalGeneration();
  
  const [broker] = useState(() => new PaperTradingBroker());
  const [executionEngine, setExecutionEngine] = useState<TradeExecutionEngine | null>(null);
  const [brokerAccount, setBrokerAccount] = useState<BrokerAccount | null>(null);
  const [executionConfig, setExecutionConfig] = useState<ExecutionConfig>(defaultExecutionConfig);
  const [executions, setExecutions] = useState<TradeExecution[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [isAutoTrading, setIsAutoTrading] = useState(false);

  // Initialize broker connection
  useEffect(() => {
    const initializeBroker = async () => {
      try {
        console.log('Initializing broker connection...');
        const result = await broker.connect();
        if (result.success) {
          setIsConnected(true);
          const accountResult = await broker.getAccount();
          if (accountResult.success) {
            setBrokerAccount(accountResult.data);
          }
        }
      } catch (error) {
        console.error('Failed to initialize broker:', error);
      }
    };

    initializeBroker();
  }, [broker]);

  // Initialize execution engine
  useEffect(() => {
    if (isConnected && account && riskParams) {
      const engine = new TradeExecutionEngine(broker, account, riskParams, executionConfig);
      setExecutionEngine(engine);
      console.log('Trade execution engine initialized');
    }
  }, [broker, isConnected, account, riskParams, executionConfig]);

  // Process new signals automatically if auto-trading is enabled
  useEffect(() => {
    if (!executionEngine || !isAutoTrading) return;

    const processNewSignals = async () => {
      const recentSignals = signals.filter(signal => 
        signal.timestamp.getTime() > Date.now() - 60000 && // Last minute
        !executions.some(exec => exec.signalId === signal.id)
      );

      for (const signal of recentSignals) {
        try {
          const execution = await executionEngine.processSignal(signal);
          setExecutions(prev => [execution, ...prev]);
        } catch (error) {
          console.error('Error processing signal:', error);
        }
      }
    };

    const interval = setInterval(processNewSignals, 5000); // Check every 5 seconds
    return () => clearInterval(interval);
  }, [executionEngine, signals, executions, isAutoTrading]);

  // Refresh broker account periodically
  useEffect(() => {
    if (!isConnected) return;

    const refreshAccount = async () => {
      try {
        const result = await broker.refreshAccount();
        if (result.success) {
          setBrokerAccount(result.data);
        }
      } catch (error) {
        console.error('Error refreshing account:', error);
      }
    };

    const interval = setInterval(refreshAccount, 10000); // Every 10 seconds
    return () => clearInterval(interval);
  }, [broker, isConnected]);

  /**
   * Toggle auto-trading on/off
   */
  const toggleAutoTrading = useCallback(() => {
    setIsAutoTrading(prev => {
      const newState = !prev;
      console.log(`Auto-trading ${newState ? 'enabled' : 'disabled'}`);
      return newState;
    });
  }, []);

  /**
   * Manually execute a specific signal
   */
  const executeSignalManually = useCallback(async (signal: MarketSignal) => {
    if (!executionEngine) {
      throw new Error('Execution engine not initialized');
    }

    try {
      const execution = await executionEngine.processSignal(signal);
      setExecutions(prev => [execution, ...prev.filter(e => e.signalId !== signal.id)]);
      return execution;
    } catch (error) {
      console.error('Error executing signal manually:', error);
      throw error;
    }
  }, [executionEngine]);

  /**
   * Update execution configuration
   */
  const updateExecutionConfig = useCallback((updates: Partial<ExecutionConfig>) => {
    setExecutionConfig(prev => {
      const newConfig = { ...prev, ...updates };
      if (executionEngine) {
        executionEngine.updateConfig(newConfig);
      }
      return newConfig;
    });
  }, [executionEngine]);

  /**
   * Get current positions from broker
   */
  const getCurrentPositions = useCallback(async () => {
    if (!broker || !isConnected) return [];
    
    try {
      const result = await broker.getPositions();
      return result.success ? result.data : [];
    } catch (error) {
      console.error('Error getting positions:', error);
      return [];
    }
  }, [broker, isConnected]);

  /**
   * Get current orders from broker
   */
  const getCurrentOrders = useCallback(async () => {
    if (!broker || !isConnected) return [];
    
    try {
      const result = await broker.getOrders();
      return result.success ? result.data : [];
    } catch (error) {
      console.error('Error getting orders:', error);
      return [];
    }
  }, [broker, isConnected]);

  /**
   * Cancel an order
   */
  const cancelOrder = useCallback(async (orderId: string) => {
    if (!broker || !isConnected) return false;
    
    try {
      const result = await broker.cancelOrder(orderId);
      if (result.success) {
        // Refresh account after cancellation
        const accountResult = await broker.refreshAccount();
        if (accountResult.success) {
          setBrokerAccount(accountResult.data);
        }
      }
      return result.success;
    } catch (error) {
      console.error('Error cancelling order:', error);
      return false;
    }
  }, [broker, isConnected]);

  /**
   * Close a position
   */
  const closePosition = useCallback(async (symbol: string, quantity?: number) => {
    if (!broker || !isConnected) return false;
    
    try {
      const result = await broker.closePosition(symbol, quantity);
      if (result.success) {
        // Refresh account after closing position
        const accountResult = await broker.refreshAccount();
        if (accountResult.success) {
          setBrokerAccount(accountResult.data);
        }
      }
      return result.success;
    } catch (error) {
      console.error('Error closing position:', error);
      return false;
    }
  }, [broker, isConnected]);

  /**
   * Get execution statistics
   */
  const getExecutionStats = useCallback(() => {
    if (!executionEngine) return null;
    return executionEngine.getExecutionStats();
  }, [executionEngine]);

  return {
    // Connection status
    isConnected,
    isAutoTrading,
    
    // Account data
    brokerAccount,
    
    // Execution data
    executions: executions.slice(0, 50), // Last 50 executions
    executionConfig,
    
    // Actions
    toggleAutoTrading,
    executeSignalManually,
    updateExecutionConfig,
    getCurrentPositions,
    getCurrentOrders,
    cancelOrder,
    closePosition,
    getExecutionStats,
    
    // Broker instance (for advanced usage)
    broker
  };
};

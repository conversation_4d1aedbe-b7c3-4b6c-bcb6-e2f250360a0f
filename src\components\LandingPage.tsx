
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, Brain, Shield, TrendingUp, Zap, Target, Star } from 'lucide-react';

const LandingPage = ({ onGetStarted }: { onGetStarted: () => void }) => {
  const features = [
    {
      icon: <Brain className="w-6 h-6 text-blue-400" />,
      title: "AI-Powered Signals",
      description: "Advanced machine learning algorithms analyze market patterns and generate high-confidence trading signals"
    },
    {
      icon: <Shield className="w-6 h-6 text-green-400" />,
      title: "Risk Management",
      description: "Automated position sizing and risk controls to protect your capital with circuit breakers"
    },
    {
      icon: <TrendingUp className="w-6 h-6 text-purple-400" />,
      title: "Real-time Analytics",
      description: "Live market data processing with sentiment analysis and technical indicators"
    },
    {
      icon: <Zap className="w-6 h-6 text-yellow-400" />,
      title: "Lightning Fast",
      description: "High-frequency trading capabilities with microsecond execution speeds"
    },
    {
      icon: <Target className="w-6 h-6 text-orange-400" />,
      title: "Precision Trading",
      description: "Multi-strategy approach with backtested algorithms for consistent performance"
    },
    {
      icon: <Star className="w-6 h-6 text-pink-400" />,
      title: "24/7 Monitoring",
      description: "Continuous market surveillance with automated alerts and notifications"
    }
  ];

  const pricingFeatures = [
    "AI-Generated Trading Signals",
    "Real-time Market Analysis",
    "Advanced Risk Management",
    "Portfolio Optimization",
    "24/7 Market Monitoring",
    "Mobile & Desktop Access",
    "API Integration",
    "Priority Support",
    "Advanced Analytics Dashboard",
    "Custom Strategy Builder"
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 text-white">
      {/* Hero Section */}
      <div className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600/20 to-purple-600/20"></div>
        <div className="relative max-w-7xl mx-auto px-6 py-20">
          <div className="text-center space-y-8">
            <Badge className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2">
              🚀 Advanced AI Trading Platform
            </Badge>
            <h1 className="text-5xl md:text-7xl font-bold">
              <span className="bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent">
                Alpha Trade Flow AI
              </span>
            </h1>
            <p className="text-xl md:text-2xl text-slate-300 max-w-3xl mx-auto leading-relaxed">
              Revolutionize your trading with AI-powered insights, automated risk management, 
              and real-time market analysis. Join the future of intelligent trading.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                onClick={onGetStarted}
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-lg px-8 py-4 h-auto"
              >
                Start Trading Now
              </Button>
              <Button variant="outline" className="border-slate-600 text-slate-300 hover:bg-slate-800 text-lg px-8 py-4 h-auto">
                Watch Demo
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="max-w-7xl mx-auto px-6 py-20">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold mb-4">
            Powered by <span className="text-blue-400">Advanced AI</span>
          </h2>
          <p className="text-xl text-slate-400">
            Everything you need to dominate the markets with artificial intelligence
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <Card key={index} className="bg-slate-800/50 border-slate-700 backdrop-blur-sm hover:bg-slate-800/70 transition-colors">
              <CardHeader>
                <div className="flex items-center gap-3">
                  {feature.icon}
                  <CardTitle className="text-xl">{feature.title}</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-slate-400">{feature.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Pricing Section */}
      <div className="max-w-4xl mx-auto px-6 py-20">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold mb-4">
            Simple <span className="text-purple-400">Pricing</span>
          </h2>
          <p className="text-xl text-slate-400">
            One plan, unlimited possibilities
          </p>
        </div>

        <Card className="bg-gradient-to-br from-slate-800/50 to-slate-900/50 border-slate-700 backdrop-blur-sm relative overflow-hidden">
          <div className="absolute top-0 right-0 bg-gradient-to-l from-purple-600 to-blue-600 text-white px-6 py-2 text-sm font-medium">
            Most Popular
          </div>
          <CardHeader className="text-center pb-8">
            <CardTitle className="text-3xl font-bold">Professional Plan</CardTitle>
            <div className="mt-4">
              <span className="text-5xl font-bold">₹4,999</span>
              <span className="text-xl text-slate-400">/month</span>
            </div>
            <p className="text-slate-400 mt-2">Everything you need for professional trading</p>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {pricingFeatures.map((feature, index) => (
                <div key={index} className="flex items-center gap-3">
                  <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
                  <span className="text-slate-300">{feature}</span>
                </div>
              ))}
            </div>
            <div className="pt-6">
              <Button 
                onClick={onGetStarted}
                className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-lg py-4 h-auto"
              >
                Start 7-Day Free Trial
              </Button>
              <p className="text-xs text-slate-500 text-center mt-2">
                No credit card required. Cancel anytime.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Stats Section */}
      <div className="bg-gradient-to-r from-slate-800/50 to-slate-900/50 py-16">
        <div className="max-w-6xl mx-auto px-6">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-3xl font-bold text-blue-400">95%</div>
              <div className="text-slate-400">Signal Accuracy</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-green-400">₹10M+</div>
              <div className="text-slate-400">Volume Traded</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-purple-400">5000+</div>
              <div className="text-slate-400">Active Users</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-yellow-400">24/7</div>
              <div className="text-slate-400">Market Coverage</div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="border-t border-slate-700 py-8">
        <div className="max-w-7xl mx-auto px-6 text-center">
          <p className="text-slate-400">
            © 2024 Alpha Trade Flow AI. All rights reserved. Built with advanced AI technology.
          </p>
        </div>
      </div>
    </div>
  );
};

export default LandingPage;

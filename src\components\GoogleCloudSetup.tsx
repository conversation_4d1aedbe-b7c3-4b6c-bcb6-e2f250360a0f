import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Cloud, 
  Key, 
  CheckCircle2, 
  AlertTriangle, 
  ExternalLink,
  Activity,
  Mic,
  MessageSquare,
  Image,
  Languages
} from 'lucide-react';
import { GoogleCloudService } from '@/services/GoogleCloudService';
import { useToast } from '@/hooks/use-toast';

const GoogleCloudSetup: React.FC = () => {
  const [apiKey, setApiKey] = useState('');
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [usageStats, setUsageStats] = useState<Record<string, any>>({});
  const { toast } = useToast();

  useEffect(() => {
    // Load saved API key
    const savedKey = localStorage.getItem('google_cloud_api_key');
    if (savedKey) {
      setApiKey(savedKey);
      GoogleCloudService.setApiKey(savedKey);
      setIsConnected(true);
      loadUsageStats();
    }
  }, []);

  const loadUsageStats = () => {
    const stats = GoogleCloudService.getUsageStats();
    setUsageStats(stats);
  };

  const handleConnect = async () => {
    if (!apiKey.trim()) {
      toast({
        title: "API Key Required",
        description: "Please enter your Google Cloud API key",
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);
    
    try {
      // Test the API key with a simple sentiment analysis
      GoogleCloudService.setApiKey(apiKey);
      await GoogleCloudService.analyzeSentiment("This is a test message for API validation.");
      
      // Save the API key
      localStorage.setItem('google_cloud_api_key', apiKey);
      setIsConnected(true);
      loadUsageStats();
      
      toast({
        title: "Connected Successfully",
        description: "Google Cloud APIs are now active",
      });
      
    } catch (error) {
      console.error('Connection failed:', error);
      toast({
        title: "Connection Failed",
        description: "Please check your API key and try again",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDisconnect = () => {
    localStorage.removeItem('google_cloud_api_key');
    setApiKey('');
    setIsConnected(false);
    setUsageStats({});
    
    toast({
      title: "Disconnected",
      description: "Google Cloud APIs have been disconnected",
    });
  };

  const getUsagePercentage = (used: number, limit: number): number => {
    return Math.round((used / limit) * 100);
  };

  const getUsageColor = (percentage: number): string => {
    if (percentage >= 90) return 'destructive';
    if (percentage >= 70) return 'warning';
    return 'default';
  };

  return (
    <div className="space-y-6">
      <Card className="backdrop-blur-md bg-card/50 border-primary/20">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Cloud className="h-5 w-5" />
            Google Cloud Integration
          </CardTitle>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {!isConnected ? (
            <div className="space-y-4">
              <Alert>
                <Key className="h-4 w-4" />
                <AlertDescription>
                  Connect your Google Cloud API key to enable advanced features like enhanced sentiment analysis, 
                  voice commands, and translation services.
                </AlertDescription>
              </Alert>

              <div className="space-y-2">
                <label className="text-sm font-medium">Google Cloud API Key</label>
                <div className="flex gap-2">
                  <Input
                    type="password"
                    placeholder="Enter your Google Cloud API key"
                    value={apiKey}
                    onChange={(e) => setApiKey(e.target.value)}
                    className="flex-1"
                  />
                  <Button 
                    onClick={handleConnect}
                    disabled={isLoading}
                    className="min-w-[100px]"
                  >
                    {isLoading ? "Connecting..." : "Connect"}
                  </Button>
                </div>
              </div>

              <div className="p-4 bg-muted/50 rounded-lg space-y-2">
                <p className="text-sm font-medium">How to get your API key:</p>
                <ol className="text-sm text-muted-foreground space-y-1 ml-4 list-decimal">
                  <li>Go to <a href="https://console.cloud.google.com/" target="_blank" rel="noopener noreferrer" className="text-primary hover:underline inline-flex items-center gap-1">Google Cloud Console <ExternalLink className="h-3 w-3" /></a></li>
                  <li>Create a new project or select existing one</li>
                  <li>Enable the required APIs (Natural Language, Translate, etc.)</li>
                  <li>Go to "Credentials" and create an API key</li>
                  <li>Restrict the key to only the APIs you need</li>
                </ol>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <CheckCircle2 className="h-5 w-5 text-green-500" />
                  <span className="font-medium">Connected to Google Cloud</span>
                </div>
                <Button variant="outline" onClick={handleDisconnect}>
                  Disconnect
                </Button>
              </div>

              {/* API Usage Statistics */}
              <Tabs defaultValue="usage" className="w-full">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="usage">Usage Stats</TabsTrigger>
                  <TabsTrigger value="features">Features</TabsTrigger>
                </TabsList>
                
                <TabsContent value="usage" className="space-y-4">
                  {Object.entries(usageStats).map(([service, stats]: [string, any]) => {
                    const percentage = getUsagePercentage(stats.used, stats.limit);
                    return (
                      <div key={service} className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="font-medium">{service.replace('_', ' ')}</span>
                          <span className="text-muted-foreground">
                            {stats.used.toLocaleString()} / {stats.limit.toLocaleString()}
                          </span>
                        </div>
                        <Progress value={percentage} className="h-2" />
                        <div className="flex justify-between text-xs text-muted-foreground">
                          <span>{percentage}% used</span>
                          <span>{stats.remaining.toLocaleString()} remaining</span>
                        </div>
                      </div>
                    );
                  })}
                  
                  <Alert>
                    <Activity className="h-4 w-4" />
                    <AlertDescription>
                      Usage resets monthly. Free tier limits apply.
                    </AlertDescription>
                  </Alert>
                </TabsContent>
                
                <TabsContent value="features" className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Card className="p-4">
                      <div className="flex items-center gap-2 mb-2">
                        <MessageSquare className="h-4 w-4" />
                        <span className="font-medium">Enhanced Sentiment Analysis</span>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Advanced NLP for better market sentiment detection
                      </p>
                      <Badge className="mt-2" variant="secondary">Active</Badge>
                    </Card>
                    
                    <Card className="p-4">
                      <div className="flex items-center gap-2 mb-2">
                        <Mic className="h-4 w-4" />
                        <span className="font-medium">Voice Commands</span>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Speech-to-text for hands-free trading commands
                      </p>
                      <Badge className="mt-2" variant="secondary">Active</Badge>
                    </Card>
                    
                    <Card className="p-4">
                      <div className="flex items-center gap-2 mb-2">
                        <Languages className="h-4 w-4" />
                        <span className="font-medium">Translation</span>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Multi-language support for global news
                      </p>
                      <Badge className="mt-2" variant="secondary">Active</Badge>
                    </Card>
                    
                    <Card className="p-4">
                      <div className="flex items-center gap-2 mb-2">
                        <Image className="h-4 w-4" />
                        <span className="font-medium">Chart Analysis</span>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        AI-powered chart pattern recognition
                      </p>
                      <Badge className="mt-2" variant="secondary">Active</Badge>
                    </Card>
                  </div>
                </TabsContent>
              </Tabs>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default GoogleCloudSetup;
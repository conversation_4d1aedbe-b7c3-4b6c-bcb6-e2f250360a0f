# ElectroRide Backend (Express) – MVP

Minimal Express server with in-memory storage for users, tasks, and rewards.

## Endpoints
- GET /api/health – healthcheck
- GET /api/users – list users (with points)
- POST /api/tasks – create task
- GET /api/tasks – list tasks
- GET /api/tasks/:id – get a task
- PUT /api/tasks/:id – update task (status, assignee, etc.)
- POST /api/tasks/:id/complete – mark completed and award points
- POST /api/webhooks/n8n – receive n8n callbacks
- POST /api/notify – mock notify endpoint (for MCP-style tool)

## Local setup
1. Open a terminal in server/
2. Install deps (requires permission):
   npm install express cors
3. Run:
   node src/index.js
4. Test:
   curl http://localhost:3000/api/health

Note: This MVP uses in-memory storage. For persistence, add MongoDB and replace the Maps with models.


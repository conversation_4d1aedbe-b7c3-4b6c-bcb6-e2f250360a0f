import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { Brain, TrendingUp, TrendingDown, Shield, Zap, Target, Volume2, AlertTriangle, CheckCircle, XCircle } from 'lucide-react';
import { GoogleCloudService } from '@/services/GoogleCloudService';
import { ElevenLabsService } from '@/services/ElevenLabsService';
import { useBrokerManager } from '@/hooks/useBrokerManager';
import { useToast } from '@/hooks/use-toast';
import { motion } from 'framer-motion';

interface SmartOrder {
  id: string;
  symbol: string;
  action: 'BUY' | 'SELL';
  quantity: number;
  orderType: 'MARKET' | 'LIMIT' | 'STOP' | 'STOP_LIMIT';
  price?: number;
  stopPrice?: number;
  aiConfidence: number;
  riskScore: number;
  reasoning: string;
  conditions: SmartCondition[];
  status: 'PENDING' | 'ACTIVE' | 'FILLED' | 'CANCELLED';
  timestamp: Date;
}

interface SmartCondition {
  type: 'PRICE' | 'VOLUME' | 'RSI' | 'NEWS_SENTIMENT' | 'TIME';
  operator: 'ABOVE' | 'BELOW' | 'EQUALS';
  value: number | string;
  met: boolean;
}

interface VoiceOrderParams {
  symbol: string;
  action: 'BUY' | 'SELL';
  quantity: number;
  price?: number;
  conditions?: string[];
}

interface RiskAssessment {
  score: number; // 0-100
  level: 'LOW' | 'MEDIUM' | 'HIGH';
  factors: string[];
  recommendations: string[];
}

export const SmartOrderManager: React.FC = () => {
  const [smartOrders, setSmartOrders] = useState<SmartOrder[]>([]);
  const [selectedSymbol, setSelectedSymbol] = useState('');
  const [selectedAction, setSelectedAction] = useState<'BUY' | 'SELL'>('BUY');
  const [quantity, setQuantity] = useState<number>(0);
  const [orderType, setOrderType] = useState<'MARKET' | 'LIMIT' | 'STOP' | 'STOP_LIMIT'>('MARKET');
  const [price, setPrice] = useState<number>(0);
  const [stopPrice, setStopPrice] = useState<number>(0);
  const [voiceOrdersEnabled, setVoiceOrdersEnabled] = useState(false);
  const [smartAnalysisEnabled, setSmartAnalysisEnabled] = useState(true);
  const [riskLevel, setRiskLevel] = useState<number[]>([50]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [currentRiskAssessment, setCurrentRiskAssessment] = useState<RiskAssessment | null>(null);
  const [pendingOrder, setPendingOrder] = useState<SmartOrder | null>(null);
  
  const { broker, getCurrentPositions, getCurrentOrders } = useBrokerManager();
  const { toast } = useToast();

  // Voice order processing
  useEffect(() => {
    if (voiceOrdersEnabled) {
      // Listen for voice commands
      const handleVoiceOrder = (event: CustomEvent<VoiceOrderParams>) => {
        processVoiceOrder(event.detail);
      };
      
      window.addEventListener('voiceOrder', handleVoiceOrder as EventListener);
      return () => window.removeEventListener('voiceOrder', handleVoiceOrder as EventListener);
    }
  }, [voiceOrdersEnabled]);

  const processVoiceOrder = async (params: VoiceOrderParams) => {
    try {
      setIsAnalyzing(true);
      
      // Analyze the voice order with AI
      const analysis = await analyzeOrderWithAI(params);
      
      const smartOrder: SmartOrder = {
        id: Date.now().toString(),
        symbol: params.symbol,
        action: params.action,
        quantity: params.quantity,
        orderType: params.price ? 'LIMIT' : 'MARKET',
        price: params.price,
        aiConfidence: analysis.confidence,
        riskScore: analysis.riskScore,
        reasoning: analysis.reasoning,
        conditions: analysis.conditions,
        status: 'PENDING',
        timestamp: new Date()
      };
      
      setPendingOrder(smartOrder);
      
      // Speak confirmation
      if (ElevenLabsService.isConfigured()) {
        const confirmationText = `Voice order received: ${params.action} ${params.quantity} shares of ${params.symbol}${params.price ? ` at $${params.price}` : ' at market price'}. Risk score: ${analysis.riskScore}%. Do you want to proceed?`;
        await speakConfirmation(confirmationText);
      }
      
    } catch (error) {
      console.error('Voice order processing error:', error);
      toast({
        title: "Voice Order Error",
        description: "Failed to process voice order",
        variant: "destructive"
      });
    } finally {
      setIsAnalyzing(false);
    }
  };

  const analyzeOrderWithAI = async (params: VoiceOrderParams) => {
    // Get current market data and portfolio context
    const positions = await getCurrentPositions();
    const orders = await getCurrentOrders();
    
    // Analyze sentiment for the symbol
    const newsText = `${params.symbol} market analysis trading sentiment`;
    const sentiment = await GoogleCloudService.analyzeSentiment(newsText);
    
    // Calculate risk score based on multiple factors
    let riskScore = 50; // Base risk
    const riskFactors: string[] = [];
    
    // Position size risk
    const existingPosition = positions.find(p => p.symbol === params.symbol);
    if (existingPosition) {
      const positionValue = existingPosition.quantity * existingPosition.currentPrice;
      if (positionValue > 10000) {
        riskScore += 15;
        riskFactors.push('Large existing position');
      }
    }
    
    // Order size risk
    if (params.quantity > 500) {
      riskScore += 20;
      riskFactors.push('Large order quantity');
    }
    
    // Market sentiment risk
    if (sentiment.label === 'NEGATIVE') {
      riskScore += 10;
      riskFactors.push('Negative market sentiment');
    } else if (sentiment.label === 'POSITIVE') {
      riskScore -= 10;
      riskFactors.push('Positive market sentiment');
    }
    
    // Time-based risk (market hours)
    const now = new Date();
    const hour = now.getHours();
    if (hour < 9 || hour > 16) {
      riskScore += 15;
      riskFactors.push('Outside regular trading hours');
    }
    
    // Generate AI reasoning
    const reasoning = `AI Analysis: ${params.action} order for ${params.quantity} ${params.symbol}. ${riskFactors.length > 0 ? 'Risk factors: ' + riskFactors.join(', ') + '. ' : ''}Market sentiment: ${sentiment.label.toLowerCase()}. Confidence: ${sentiment.confidence.toFixed(2)}.`;
    
    // Generate smart conditions
    const conditions: SmartCondition[] = [];
    
    if (params.price) {
      conditions.push({
        type: 'PRICE',
        operator: params.action === 'BUY' ? 'BELOW' : 'ABOVE',
        value: params.price,
        met: false
      });
    }
    
    // Add volume condition for large orders
    if (params.quantity > 100) {
      conditions.push({
        type: 'VOLUME',
        operator: 'ABOVE',
        value: params.quantity * 2,
        met: false
      });
    }
    
    return {
      confidence: Math.max(0, Math.min(100, 100 - riskScore + (sentiment.confidence * 30))),
      riskScore: Math.max(0, Math.min(100, riskScore)),
      reasoning,
      conditions
    };
  };

  const speakConfirmation = async (text: string) => {
    try {
      const audioBuffer = await ElevenLabsService.textToSpeech(text, {
        voiceId: 'EXAVITQu4vr4xnSDxMaL', // Sarah - professional voice
        voiceSettings: {
          stability: 0.7,
          similarity_boost: 0.8,
          style: 0.2
        }
      });
      await ElevenLabsService.playAudio(audioBuffer);
    } catch (error) {
      console.error('TTS error:', error);
    }
  };

  const createSmartOrder = async () => {
    if (!selectedSymbol || quantity <= 0) {
      toast({
        title: "Invalid Order",
        description: "Please fill in all required fields",
        variant: "destructive"
      });
      return;
    }
    
    setIsAnalyzing(true);
    
    try {
      const analysis = await analyzeOrderWithAI({
        symbol: selectedSymbol,
        action: selectedAction,
        quantity,
        price: orderType !== 'MARKET' ? price : undefined
      });
      
      const smartOrder: SmartOrder = {
        id: Date.now().toString(),
        symbol: selectedSymbol,
        action: selectedAction,
        quantity,
        orderType,
        price: orderType !== 'MARKET' ? price : undefined,
        stopPrice: orderType === 'STOP_LIMIT' ? stopPrice : undefined,
        aiConfidence: analysis.confidence,
        riskScore: analysis.riskScore,
        reasoning: analysis.reasoning,
        conditions: analysis.conditions,
        status: 'PENDING',
        timestamp: new Date()
      };
      
      setPendingOrder(smartOrder);
      
    } catch (error) {
      console.error('Order analysis error:', error);
      toast({
        title: "Analysis Error",
        description: "Failed to analyze order",
        variant: "destructive"
      });
    } finally {
      setIsAnalyzing(false);
    }
  };

  const confirmOrder = async (order: SmartOrder) => {
    try {
      // Execute order through broker
      const result = await broker.placeOrder({
        symbol: order.symbol,
        quantity: order.quantity,
        side: order.action,
        type: order.orderType,
        price: order.price,
        stopPrice: order.stopPrice
      });
      
      if (result.success) {
        const confirmedOrder = { ...order, status: 'ACTIVE' as const };
        setSmartOrders(prev => [...prev, confirmedOrder]);
        setPendingOrder(null);
        
        toast({
          title: "Order Placed",
          description: `${order.action} order for ${order.quantity} ${order.symbol} has been placed`,
        });
        
        // Speak confirmation
        if (ElevenLabsService.isConfigured()) {
          await speakConfirmation(`Order confirmed: ${order.action} ${order.quantity} shares of ${order.symbol}`);
        }
        
        // Reset form
        setSelectedSymbol('');
        setQuantity(0);
        setPrice(0);
        setStopPrice(0);
        
      } else {
        toast({
          title: "Order Failed",
          description: result.error || "Failed to place order",
          variant: "destructive"
        });
      }
      
    } catch (error) {
      console.error('Order execution error:', error);
      toast({
        title: "Execution Error",
        description: "Failed to execute order",
        variant: "destructive"
      });
    }
  };

  const cancelOrder = async (orderId: string) => {
    try {
      const result = await broker.cancelOrder(orderId);
      if (result.success) {
        setSmartOrders(prev => 
          prev.map(order => 
            order.id === orderId 
              ? { ...order, status: 'CANCELLED' as const }
              : order
          )
        );
        
        toast({
          title: "Order Cancelled",
          description: "Order has been successfully cancelled",
        });
      }
    } catch (error) {
      console.error('Cancel order error:', error);
      toast({
        title: "Cancel Error",
        description: "Failed to cancel order",
        variant: "destructive"
      });
    }
  };

  const getRiskLevelColor = (score: number) => {
    if (score < 30) return 'text-green-600';
    if (score < 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getRiskLevelBadge = (score: number) => {
    if (score < 30) return 'default';
    if (score < 70) return 'secondary';
    return 'destructive';
  };

  return (
    <div className="space-y-6">
      <Card className="backdrop-blur-md bg-card/50 border-primary/20">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            Smart Order Manager
          </CardTitle>
        </CardHeader>
        
        <CardContent>
          <Tabs defaultValue="create" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="create">Create Order</TabsTrigger>
              <TabsTrigger value="active">Active Orders</TabsTrigger>
              <TabsTrigger value="settings">Settings</TabsTrigger>
            </TabsList>
            
            <TabsContent value="create" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="symbol">Symbol</Label>
                    <Input
                      id="symbol"
                      value={selectedSymbol}
                      onChange={(e) => setSelectedSymbol(e.target.value.toUpperCase())}
                      placeholder="e.g., AAPL"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="action">Action</Label>
                    <Select value={selectedAction} onValueChange={(value: 'BUY' | 'SELL') => setSelectedAction(value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="BUY">BUY</SelectItem>
                        <SelectItem value="SELL">SELL</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label htmlFor="quantity">Quantity</Label>
                    <Input
                      id="quantity"
                      type="number"
                      value={quantity}
                      onChange={(e) => setQuantity(Number(e.target.value))}
                      placeholder="Number of shares"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="orderType">Order Type</Label>
                    <Select value={orderType} onValueChange={(value: any) => setOrderType(value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="MARKET">Market</SelectItem>
                        <SelectItem value="LIMIT">Limit</SelectItem>
                        <SelectItem value="STOP">Stop</SelectItem>
                        <SelectItem value="STOP_LIMIT">Stop Limit</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  {orderType !== 'MARKET' && (
                    <div>
                      <Label htmlFor="price">Price</Label>
                      <Input
                        id="price"
                        type="number"
                        step="0.01"
                        value={price}
                        onChange={(e) => setPrice(Number(e.target.value))}
                        placeholder="Order price"
                      />
                    </div>
                  )}
                  
                  {orderType === 'STOP_LIMIT' && (
                    <div>
                      <Label htmlFor="stopPrice">Stop Price</Label>
                      <Input
                        id="stopPrice"
                        type="number"
                        step="0.01"
                        value={stopPrice}
                        onChange={(e) => setStopPrice(Number(e.target.value))}
                        placeholder="Stop price"
                      />
                    </div>
                  )}
                </div>
                
                <div className="space-y-4">
                  <div className="p-4 bg-muted rounded-lg">
                    <h3 className="font-semibold mb-2 flex items-center gap-2">
                      <Shield className="h-4 w-4" />
                      Risk Assessment
                    </h3>
                    
                    {currentRiskAssessment ? (
                      <div className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span>Risk Score:</span>
                          <Badge variant={getRiskLevelBadge(currentRiskAssessment.score)}>
                            {currentRiskAssessment.score}%
                          </Badge>
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {currentRiskAssessment.factors.join(', ')}
                        </div>
                      </div>
                    ) : (
                      <p className="text-sm text-muted-foreground">
                        Create an order to see risk assessment
                      </p>
                    )}
                  </div>
                  
                  <div className="p-4 bg-muted rounded-lg">
                    <h3 className="font-semibold mb-2 flex items-center gap-2">
                      <Target className="h-4 w-4" />
                      AI Insights
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      {smartAnalysisEnabled 
                        ? "AI analysis will be performed on order creation"
                        : "AI analysis is disabled"
                      }
                    </p>
                  </div>
                </div>
              </div>
              
              <Button 
                onClick={createSmartOrder}
                disabled={isAnalyzing || !selectedSymbol || quantity <= 0}
                className="w-full"
              >
                {isAnalyzing ? (
                  <>
                    <Brain className="h-4 w-4 mr-2 animate-spin" />
                    Analyzing Order...
                  </>
                ) : (
                  <>
                    <Zap className="h-4 w-4 mr-2" />
                    Create Smart Order
                  </>
                )}
              </Button>
            </TabsContent>
            
            <TabsContent value="active" className="space-y-4">
              <ScrollArea className="h-96">
                {smartOrders.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <Brain className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p>No smart orders yet</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {smartOrders.map((order) => (
                      <motion.div
                        key={order.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="border rounded-lg p-4"
                      >
                        <div className="flex justify-between items-start mb-2">
                          <div className="flex items-center gap-2">
                            <Badge variant={order.action === 'BUY' ? 'default' : 'destructive'}>
                              {order.action}
                            </Badge>
                            <span className="font-semibold">{order.symbol}</span>
                            <span className="text-sm text-muted-foreground">
                              {order.quantity} shares
                            </span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge variant={getRiskLevelBadge(order.riskScore)}>
                              {order.riskScore}% Risk
                            </Badge>
                            <Badge variant="outline">
                              {order.status}
                            </Badge>
                          </div>
                        </div>
                        
                        <div className="space-y-2">
                          <div className="flex items-center gap-2 text-sm">
                            <TrendingUp className="h-3 w-3" />
                            <span>Confidence: {order.aiConfidence}%</span>
                          </div>
                          
                          {order.price && (
                            <div className="flex items-center gap-2 text-sm">
                              <Target className="h-3 w-3" />
                              <span>Price: ${order.price}</span>
                            </div>
                          )}
                          
                          <p className="text-xs text-muted-foreground">
                            {order.reasoning}
                          </p>
                          
                          {order.conditions.length > 0 && (
                            <div className="flex gap-1 flex-wrap">
                              {order.conditions.map((condition, idx) => (
                                <Badge key={idx} variant="outline" className="text-xs">
                                  {condition.type}: {condition.operator} {condition.value}
                                  {condition.met ? (
                                    <CheckCircle className="h-3 w-3 ml-1 text-green-500" />
                                  ) : (
                                    <XCircle className="h-3 w-3 ml-1 text-red-500" />
                                  )}
                                </Badge>
                              ))}
                            </div>
                          )}
                        </div>
                        
                        {order.status === 'ACTIVE' && (
                          <div className="flex gap-2 mt-3">
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => cancelOrder(order.id)}
                            >
                              Cancel
                            </Button>
                          </div>
                        )}
                      </motion.div>
                    ))}
                  </div>
                )}
              </ScrollArea>
            </TabsContent>
            
            <TabsContent value="settings" className="space-y-4">
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Voice-Activated Orders</Label>
                    <p className="text-sm text-muted-foreground">
                      Enable voice commands for order placement
                    </p>
                  </div>
                  <Switch
                    checked={voiceOrdersEnabled}
                    onCheckedChange={setVoiceOrdersEnabled}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Smart Analysis</Label>
                    <p className="text-sm text-muted-foreground">
                      Enable AI-powered order analysis
                    </p>
                  </div>
                  <Switch
                    checked={smartAnalysisEnabled}
                    onCheckedChange={setSmartAnalysisEnabled}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label>Risk Tolerance</Label>
                  <Slider
                    value={riskLevel}
                    onValueChange={setRiskLevel}
                    max={100}
                    step={1}
                    className="w-full"
                  />
                  <div className="flex justify-between text-sm text-muted-foreground">
                    <span>Conservative</span>
                    <span>{riskLevel[0]}%</span>
                    <span>Aggressive</span>
                  </div>
                </div>
                
                <div className="p-4 bg-muted rounded-lg">
                  <h3 className="font-semibold mb-2">Voice Commands</h3>
                  <div className="text-sm space-y-1">
                    <p>• "Buy 100 AAPL at market"</p>
                    <p>• "Sell 50 TSLA at limit 250"</p>
                    <p>• "Stop loss MSFT at 300"</p>
                    <p>• "Cancel all orders"</p>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
      
      {/* Order Confirmation Dialog */}
      {pendingOrder && (
        <AlertDialog open={!!pendingOrder} onOpenChange={() => setPendingOrder(null)}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Confirm Smart Order</AlertDialogTitle>
              <AlertDialogDescription asChild>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="font-semibold">
                      {pendingOrder.action} {pendingOrder.quantity} {pendingOrder.symbol}
                    </span>
                    <Badge variant={getRiskLevelBadge(pendingOrder.riskScore)}>
                      {pendingOrder.riskScore}% Risk
                    </Badge>
                  </div>
                  
                  {pendingOrder.price && (
                    <p>Price: ${pendingOrder.price}</p>
                  )}
                  
                  <div className="flex items-center gap-2">
                    <TrendingUp className="h-4 w-4" />
                    <span>AI Confidence: {pendingOrder.aiConfidence}%</span>
                  </div>
                  
                  <p className="text-sm">{pendingOrder.reasoning}</p>
                  
                  {pendingOrder.riskScore > 70 && (
                    <div className="flex items-center gap-2 p-2 bg-red-50 rounded text-red-700">
                      <AlertTriangle className="h-4 w-4" />
                      <span className="text-sm">High risk order detected</span>
                    </div>
                  )}
                </div>
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction onClick={() => confirmOrder(pendingOrder)}>
                Confirm Order
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      )}
    </div>
  );
};
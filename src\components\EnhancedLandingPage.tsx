import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import {
  Brain,
  TrendingUp,
  Shield,
  Zap,
  Star,
  ArrowRight,
  Check,
  BarChart3,
  Target,
  Sparkles,
  Globe,
  ChevronRight,
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ThemeToggle } from './ThemeToggle';

interface EnhancedLandingPageProps {
  onGetStarted: () => void;
}

const AnimatedCounter = ({ target, duration = 2000 }: { target: number; duration?: number }) => {
  const [count, setCount] = useState(0);
  const { ref, inView } = useInView({ threshold: 0.5 });

  useEffect(() => {
    if (inView) {
      let start = 0;
      const increment = target / (duration / 50);
      const timer = setInterval(() => {
        start += increment;
        if (start >= target) {
          setCount(target);
          clearInterval(timer);
        } else {
          setCount(Math.floor(start));
        }
      }, 50);
      return () => clearInterval(timer);
    }
  }, [inView, target, duration]);

  return <span ref={ref}>{count.toLocaleString()}</span>;
};

const FloatingElement = ({ children, delay = 0 }: { children: React.ReactNode; delay?: number }) => (
  <motion.div
    initial={{ y: 0 }}
    animate={{ y: [-10, 10, -10] }}
    transition={{ 
      duration: 6, 
      repeat: Infinity, 
      ease: "easeInOut",
      delay 
    }}
    className="absolute"
  >
    {children}
  </motion.div>
);

const features = [
  {
    icon: Brain,
    title: "AI-Powered Analysis",
    description: "Advanced machine learning algorithms analyze market patterns and sentiment in real-time.",
    color: "text-blue-400",
    bgColor: "bg-blue-500/10"
  },
  {
    icon: TrendingUp,
    title: "Smart Trading Signals",
    description: "Get precise buy/sell signals with confidence scores and detailed reasoning.",
    color: "text-green-400",
    bgColor: "bg-green-500/10"
  },
  {
    icon: Shield,
    title: "Risk Management",
    description: "Built-in risk assessment and portfolio protection with stop-loss automation.",
    color: "text-orange-400",
    bgColor: "bg-orange-500/10"
  },
  {
    icon: Zap,
    title: "Real-Time Execution",
    description: "Lightning-fast trade execution with multiple broker integrations.",
    color: "text-yellow-400",
    bgColor: "bg-yellow-500/10"
  },
  {
    icon: BarChart3,
    title: "Advanced Analytics",
    description: "Comprehensive market analysis with customizable charts and indicators.",
    color: "text-purple-400",
    bgColor: "bg-purple-500/10"
  },
  {
    icon: Target,
    title: "Strategy Automation",
    description: "Automate complex trading strategies with visual workflow builder.",
    color: "text-pink-400",
    bgColor: "bg-pink-500/10"
  }
];

const pricingFeatures = [
  "Unlimited AI signals",
  "Real-time market data",
  "Advanced risk management",
  "Multi-broker support",
  "24/7 automated trading",
  "Priority customer support",
  "Advanced analytics dashboard",
  "Custom strategy builder"
];

const testimonials = [
  {
    name: "Rajesh Kumar",
    role: "Professional Trader",
    image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face",
    quote: "Alpha Trade Flow AI has transformed my trading. The AI signals are incredibly accurate, and I've seen a 40% improvement in my returns."
  },
  {
    name: "Priya Sharma",
    role: "Investment Advisor",
    image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face",
    quote: "The risk management features are outstanding. I can sleep peacefully knowing my portfolio is protected 24/7."
  },
  {
    name: "Amit Patel",
    role: "Quantitative Analyst",
    image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face",
    quote: "The strategy automation capabilities are game-changing. I've automated my entire trading workflow seamlessly."
  }
];

export default function EnhancedLandingPage({ onGetStarted }: EnhancedLandingPageProps) {
  const [currentTestimonial, setCurrentTestimonial] = useState(0);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
    }, 5000);
    return () => clearInterval(timer);
  }, []);

  return (
    <div className="min-h-screen bg-gradient-hero text-foreground overflow-hidden">
      {/* Floating Background Elements */}
      <div className="fixed inset-0 pointer-events-none">
        <FloatingElement delay={0}>
          <div className="top-20 left-20 w-32 h-32 bg-primary/10 rounded-full blur-xl" />
        </FloatingElement>
        <FloatingElement delay={2}>
          <div className="top-40 right-32 w-24 h-24 bg-accent/10 rounded-full blur-xl" />
        </FloatingElement>
        <FloatingElement delay={4}>
          <div className="bottom-32 left-1/4 w-40 h-40 bg-warning/10 rounded-full blur-xl" />
        </FloatingElement>
      </div>

      {/* Navigation */}
      <motion.nav 
        initial={{ y: -100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        className="relative z-50 p-6"
      >
        <div className="max-w-7xl mx-auto flex justify-between items-center">
          <motion.div 
            className="flex items-center space-x-2"
            whileHover={{ scale: 1.05 }}
          >
            <div className="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
              <Sparkles className="w-5 h-5 text-white" />
            </div>
            <span className="text-xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
              Alpha Trade Flow AI
            </span>
          </motion.div>
          
          <div className="flex items-center space-x-4">
            <ThemeToggle />
            <Button 
              onClick={onGetStarted}
              className="glass-button bg-gradient-primary hover:shadow-glow"
            >
              Get Started
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </div>
        </div>
      </motion.nav>

      {/* Hero Section */}
      <motion.section 
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        className="relative z-10 px-6 py-20"
      >
        <div className="max-w-7xl mx-auto text-center">
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ delay: 0.2 }}
            className="mb-8"
          >
            <Badge className="mb-4 glass border-0 text-primary">
              <Sparkles className="w-4 h-4 mr-2" />
              AI-Powered Trading Platform
            </Badge>
            <h1 className="text-5xl md:text-7xl font-bold mb-6">
              Trade Smarter with
              <span className="block bg-gradient-to-r from-primary via-accent to-warning bg-clip-text text-transparent animate-glow">
                Artificial Intelligence
              </span>
            </h1>
            <p className="text-xl md:text-2xl text-muted-foreground mb-8 max-w-3xl mx-auto">
              Experience the future of trading with our advanced AI algorithms that analyze markets, 
              generate signals, and execute trades with superhuman precision.
            </p>
          </motion.div>

          <motion.div 
            className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16"
            initial={{ y: 50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.6 }}
          >
            <Button 
              size="lg" 
              onClick={onGetStarted}
              className="glass-button bg-gradient-primary hover:shadow-glow hover:scale-105 px-8 py-4 text-lg"
            >
              Start Trading Now
              <ArrowRight className="w-5 h-5 ml-2" />
            </Button>
            <Button 
              size="lg" 
              variant="outline" 
              className="glass-button border-0 px-8 py-4 text-lg"
            >
              Watch Demo
              <Globe className="w-5 h-5 ml-2" />
            </Button>
          </motion.div>

          {/* Stats */}
          <motion.div 
            className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto"
            initial={{ y: 50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.8 }}
          >
            <div className="glass-card p-6">
              <div className="text-3xl font-bold text-primary mb-2">
                <AnimatedCounter target={50000} />+
              </div>
              <p className="text-muted-foreground">Active Traders</p>
            </div>
            <div className="glass-card p-6">
              <div className="text-3xl font-bold text-accent mb-2">
                <AnimatedCounter target={89} />%
              </div>
              <p className="text-muted-foreground">Success Rate</p>
            </div>
            <div className="glass-card p-6">
              <div className="text-3xl font-bold text-warning mb-2">
                ₹<AnimatedCounter target={10000} />Cr+
              </div>
              <p className="text-muted-foreground">Volume Traded</p>
            </div>
          </motion.div>
        </div>
      </motion.section>

      {/* Features Section */}
      <motion.section 
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        transition={{ duration: 0.8 }}
        className="relative z-10 px-6 py-20"
      >
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              Powerful Features for
              <span className="block text-primary">Professional Trading</span>
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Our comprehensive suite of AI-powered tools gives you the edge in today's competitive markets.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ y: 50, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ y: -10 }}
                className="group"
              >
                <Card className="glass-card border-0 hover:shadow-glow h-full">
                  <CardHeader>
                    <div className={`w-12 h-12 rounded-lg ${feature.bgColor} flex items-center justify-center mb-4 group-hover:scale-110 transition-transform`}>
                      <feature.icon className={`w-6 h-6 ${feature.color}`} />
                    </div>
                    <CardTitle className="text-xl mb-2">{feature.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground">{feature.description}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.section>

      {/* Testimonials Section */}
      <motion.section 
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        className="relative z-10 px-6 py-20"
      >
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl font-bold mb-16">
            Trusted by <span className="text-primary">Thousands</span> of Traders
          </h2>
          
          <motion.div 
            key={currentTestimonial}
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -50 }}
            className="glass-card p-8"
          >
            <div className="flex items-center justify-center mb-6">
              {[...Array(5)].map((_, i) => (
                <Star key={i} className="w-5 h-5 text-warning fill-current" />
              ))}
            </div>
            <blockquote className="text-xl md:text-2xl font-medium mb-6">
              "{testimonials[currentTestimonial].quote}"
            </blockquote>
            <div className="flex items-center justify-center space-x-4">
              <img 
                src={testimonials[currentTestimonial].image}
                alt={testimonials[currentTestimonial].name}
                className="w-12 h-12 rounded-full"
              />
              <div className="text-left">
                <div className="font-semibold">{testimonials[currentTestimonial].name}</div>
                <div className="text-muted-foreground text-sm">{testimonials[currentTestimonial].role}</div>
              </div>
            </div>
          </motion.div>

          <div className="flex justify-center space-x-2 mt-8">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentTestimonial(index)}
                className={`w-3 h-3 rounded-full transition-colors ${
                  index === currentTestimonial ? 'bg-primary' : 'bg-muted'
                }`}
              />
            ))}
          </div>
        </div>
      </motion.section>

      {/* Pricing Section */}
      <motion.section 
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        className="relative z-10 px-6 py-20"
      >
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              Simple, Transparent
              <span className="block text-primary">Pricing</span>
            </h2>
            <p className="text-xl text-muted-foreground">
              Start your AI trading journey today. No hidden fees, cancel anytime.
            </p>
          </div>

          <motion.div 
            whileHover={{ scale: 1.02 }}
            className="glass-card p-8 max-w-lg mx-auto"
          >
            <div className="text-center mb-8">
              <Badge className="mb-4 bg-gradient-primary text-white">Most Popular</Badge>
              <h3 className="text-3xl font-bold mb-2">Pro Trader</h3>
              <div className="flex items-center justify-center mb-4">
                <span className="text-5xl font-bold text-primary">₹2,999</span>
                <span className="text-muted-foreground ml-2">/month</span>
              </div>
              <p className="text-muted-foreground">
                Everything you need for professional AI-powered trading
              </p>
            </div>

            <div className="space-y-4 mb-8">
              {pricingFeatures.map((feature, index) => (
                <div key={index} className="flex items-center">
                  <Check className="w-5 h-5 text-accent mr-3" />
                  <span>{feature}</span>
                </div>
              ))}
            </div>

            <Button 
              size="lg" 
              onClick={onGetStarted}
              className="w-full glass-button bg-gradient-primary hover:shadow-glow"
            >
              Start Free Trial
              <ChevronRight className="w-4 h-4 ml-2" />
            </Button>
            
            <p className="text-center text-sm text-muted-foreground mt-4">
              14-day free trial • No credit card required
            </p>
          </motion.div>
        </div>
      </motion.section>

      {/* CTA Section */}
      <motion.section 
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        className="relative z-10 px-6 py-20"
      >
        <div className="max-w-4xl mx-auto text-center">
          <div className="glass-card p-12">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              Ready to Transform Your
              <span className="block bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
                Trading Experience?
              </span>
            </h2>
            <p className="text-xl text-muted-foreground mb-8">
              Join thousands of successful traders who trust Alpha Trade Flow AI 
              to maximize their profits and minimize their risks.
            </p>
            <Button 
              size="lg" 
              onClick={onGetStarted}
              className="glass-button bg-gradient-primary hover:shadow-glow px-12 py-4 text-lg"
            >
              Get Started Today
              <ArrowRight className="w-5 h-5 ml-2" />
            </Button>
          </div>
        </div>
      </motion.section>
    </div>
  );
}
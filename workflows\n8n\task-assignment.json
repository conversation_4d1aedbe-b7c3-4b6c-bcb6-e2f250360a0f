{"name": "ElectroRide Task Assignment (MVP)", "nodes": [{"parameters": {"httpMethod": "POST", "path": "electroride/task", "options": {}}, "id": "Webhook_Trigger", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [200, 300]}, {"parameters": {"url": "http://localhost:11434/api/chat", "options": {}, "sendBody": true, "authentication": "none", "jsonParameters": true, "options.query": {}, "options.headers": {}, "options.bodyParametersJson": "={\n  \"model\": \"llama3.1:8b\",\n  \"messages\": [\n    { \"role\": \"user\", \"content\": \"You are an assignment agent. Given the task description: {{$json.description}}, suggest an assignee among [driver1, driver2, driver3] and a short reason. Respond JSON: {\\\"assignee\\\":\\\"driverX\\\", \\\"eta\\\":10, \\\"priceBreakdown\\\":{\\\"baseFare\\\":100, \\\"distance\\\":30, \\\"time\\\":15, \\\"dynamicPricing\\\":5, \\\"serviceFee\\\":10}}\" }\n  ]\n}"}, "id": "HTTP_Ollama", "name": "<PERSON><PERSON><PERSON> (Chat)", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [460, 300]}, {"parameters": {"functionCode": "// Extract model JSON from Ollama chat response\nconst res = items[0].json;\n// Attempt to parse JSON content from the final message\nlet content = (res.message && res.message.content) || '';\nlet parsed;\ntry { parsed = JSON.parse(content); } catch(e) { parsed = { assignee: 'driver1', eta: 7, priceBreakdown: { baseFare:100, distance:30, time:15, dynamicPricing:5, serviceFee:10 } }; }\nreturn [{ json: { assignment: parsed } }];"}, "id": "Function_Map", "name": "Map Assignment", "type": "n8n-nodes-base.function", "typeVersion": 2, "position": [700, 300]}, {"parameters": {"url": "http://localhost:3000/api/notify", "authentication": "none", "jsonParameters": true, "options": {}, "options.bodyParametersJson": "={ \n  \"driverId\": {{$json.assignment.assignee}},\n  \"message\": \"New task assigned to you\"\n}"}, "id": "HTTP_Notify", "name": "Notify Driver", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [940, 240]}, {"parameters": {"url": "http://localhost:3000/api/tasks/{{$json.taskId}}", "authentication": "none", "jsonParameters": true, "options": {}, "options.bodyParametersJson": "={ \n  \"status\": \"assigned\",\n  \"assignee\": {{$json.assignment.assignee}},\n  \"metadata\": { \n    \"eta\": {{$json.assignment.eta}}, \n    \"priceBreakdown\": {{$json.assignment.priceBreakdown}} \n  }\n}"}, "id": "HTTP_Update", "name": "Update Task", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [940, 360]}, {"parameters": {"responseBody": "={\n  \"ok\": true,\n  \"assignment\": $json.assignment\n}", "responseCode": 200}, "id": "Respond", "name": "Respond", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1180, 300]}], "connections": {"Webhook_Trigger": {"main": [[{"node": "<PERSON><PERSON><PERSON> (Chat)", "type": "main", "index": 0}]]}, "Ollama (Chat)": {"main": [[{"node": "Map Assignment", "type": "main", "index": 0}]]}, "Map Assignment": {"main": [[{"node": "Notify Driver", "type": "main", "index": 0}, {"node": "Update Task", "type": "main", "index": 0}]]}, "Notify Driver": {"main": [[{"node": "Respond", "type": "main", "index": 0}]]}, "Update Task": {"main": [[{"node": "Respond", "type": "main", "index": 0}]]}}}
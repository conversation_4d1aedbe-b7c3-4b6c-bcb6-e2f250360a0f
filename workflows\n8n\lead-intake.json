{"name": "Lead Intake Automation (v0-doall-ai)", "nodes": [{"parameters": {"httpMethod": "POST", "path": "lead/new", "options": {}}, "id": "Webhook_Lead", "name": "<PERSON><PERSON><PERSON> (Lead)", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [200, 280]}, {"parameters": {"url": "http://localhost:11434/api/chat", "sendBody": true, "authentication": "none", "jsonParameters": true, "options": {}, "options.bodyParametersJson": "={\n  \"model\": \"llama3.1:8b\",\n  \"messages\": [\n    { \"role\": \"user\", \"content\": \"You are a lead router. Classify this lead's intent (trial/demo/support/other), priority (high/med/low), and suggest an assignee among [driver1, driver2, driver3]. Input: name={{$json.name}}, email={{$json.email}}, phone={{$json.phone}}, source={{$json.source}}, notes={{$json.notes}}. Respond STRICT JSON only: {\\\"intent\\\":\\\"...\\\", \\\"priority\\\":\\\"...\\\", \\\"assignee\\\":\\\"driverX\\\"}\" }\n  ]\n}"}, "id": "HTTP_Ollama", "name": "Ollama Classify", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [480, 280]}, {"parameters": {"functionCode": "// Parse JSON from Ollama response\nconst res = items[0].json;\nlet content = (res.message && res.message.content) || '';\nlet parsed;\ntry { parsed = JSON.parse(content); } catch (e) { parsed = { intent: 'trial', priority: 'med', assignee: 'driver1' }; }\n// pass through original input fields, including taskId if provided\nreturn [{ json: { classification: parsed, taskId: $json.taskId, lead: { name: $json.name, email: $json.email, phone: $json.phone, source: $json.source, notes: $json.notes } } }];"}, "id": "Function_Map", "name": "Map Classification", "type": "n8n-nodes-base.function", "typeVersion": 2, "position": [720, 280]}, {"parameters": {"url": "http://localhost:3000/api/notify", "authentication": "none", "jsonParameters": true, "options": {"headers": {"X-API-Key": "={{$env.BACKEND_API_KEY}}"}}, "options.bodyParametersJson": "={ \n  \"driverId\": {{$json.classification.assignee}},\n  \"message\": \"New lead: {{$json.lead.name}} ({{$json.lead.email}}) intent={{$json.classification.intent}} priority={{$json.classification.priority}}\"\n}"}, "id": "HTTP_Notify", "name": "Notify Owner", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [960, 220]}, {"parameters": {"url": "http://localhost:3000/api/tasks/{{$json.taskId}}", "authentication": "none", "jsonParameters": true, "options": {"headers": {"X-API-Key": "={{$env.BACKEND_API_KEY}}"}}, "options.bodyParametersJson": "={ \n  \"status\": \"assigned\",\n  \"assignee\": {{$json.classification.assignee}},\n  \"metadata\": { \n    \"intent\": {{$json.classification.intent}}, \n    \"priority\": {{$json.classification.priority}},\n    \"lead\": {{$json.lead}} \n  }\n}"}, "id": "HTTP_Update", "name": "Update Task", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [960, 340]}, {"parameters": {"responseBody": "={\n  \"ok\": true,\n  \"taskId\": $json.taskId,\n  \"classification\": $json.classification\n}", "responseCode": 200}, "id": "Respond", "name": "Respond", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1200, 280]}], "connections": {"Webhook (Lead)": {"main": [[{"node": "Ollama Classify", "type": "main", "index": 0}]]}, "Ollama Classify": {"main": [[{"node": "Map Classification", "type": "main", "index": 0}]]}, "Map Classification": {"main": [[{"node": "Notify Owner", "type": "main", "index": 0}, {"node": "Update Task", "type": "main", "index": 0}]]}, "Notify Owner": {"main": [[{"node": "Respond", "type": "main", "index": 0}]]}, "Update Task": {"main": [[{"node": "Respond", "type": "main", "index": 0}]]}}}

import { secureStorage } from './SecureStorage';
import { checkRateLimit } from '@/lib/security';

export interface MarketData {
  symbol: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  timestamp: Date;
  source: string;
}

export interface HistoricalData {
  symbol: string;
  data: Array<{
    date: string;
    open: number;
    high: number;
    low: number;
    close: number;
    volume: number;
  }>;
  source: string;
}

class MarketDataService {
  private availableSources: string[] = ['Yahoo Finance']; // Always available
  private rateLimitTracker = new Map<string, number>();

  async saveApiKey(service: string, apiKey: string): Promise<void> {
    try {
      await secureStorage.setSecureItem(`api_key_${service.toLowerCase()}`, apiKey, true);
      this.updateAvailableSources();
    } catch (error) {
      console.error('Failed to save API key securely:', error);
      throw new Error('Failed to save API key');
    }
  }

  async getApiKey(service: string): Promise<string | null> {
    try {
      return await secureStorage.getSecureItem(`api_key_${service.toLowerCase()}`);
    } catch (error) {
      console.error('Failed to retrieve API key:', error);
      return null;
    }
  }

  private async updateAvailableSources(): Promise<void> {
    const sources = ['Yahoo Finance']; // Base free source
    
    try {
      if (await this.getApiKey('alpha_vantage')) sources.push('Alpha Vantage');
      if (await this.getApiKey('finnhub')) sources.push('Finnhub');
      if (await this.getApiKey('iex_cloud')) sources.push('IEX Cloud');
    } catch (error) {
      console.error('Error updating available sources:', error);
    }
    
    this.availableSources = sources;
  }

  getAvailableSources(): string[] {
    return [...this.availableSources];
  }

  private checkApiRateLimit(source: string): boolean {
    const now = Date.now();
    const lastCall = this.rateLimitTracker.get(source) || 0;
    const minInterval = this.getMinInterval(source);
    
    if (now - lastCall < minInterval) {
      return false;
    }
    
    this.rateLimitTracker.set(source, now);
    return true;
  }

  private getMinInterval(source: string): number {
    switch (source.toLowerCase()) {
      case 'alpha_vantage': return 12000; // 5 calls per minute
      case 'finnhub': return 1000; // 60 calls per minute
      case 'iex_cloud': return 100; // Conservative rate limiting
      default: return 1000;
    }
  }

  async getMarketData(symbols: string[]): Promise<MarketData[]> {
    if (!checkRateLimit('market_data_fetch', 10, 60000)) {
      throw new Error('Rate limit exceeded for market data requests');
    }

    const results: MarketData[] = [];
    
    for (const symbol of symbols.slice(0, 10)) { // Limit to 10 symbols per request
      try {
        const data = await this.fetchFromAvailableSources(symbol);
        if (data) {
          results.push(data);
        }
      } catch (error) {
        console.error(`Failed to fetch data for ${symbol}:`, error);
      }
    }
    
    return results;
  }

  private async fetchFromAvailableSources(symbol: string): Promise<MarketData | null> {
    // Try Yahoo Finance first (always available)
    try {
      return await this.fetchFromYahoo(symbol);
    } catch (error) {
      console.error('Yahoo Finance failed:', error);
    }

    // Try other sources if API keys are available
    try {
      const alphaVantageKey = await this.getApiKey('alpha_vantage');
      if (alphaVantageKey && this.checkApiRateLimit('alpha_vantage')) {
        return await this.fetchFromAlphaVantage(symbol, alphaVantageKey);
      }
    } catch (error) {
      console.error('Alpha Vantage failed:', error);
    }

    try {
      const finnhubKey = await this.getApiKey('finnhub');
      if (finnhubKey && this.checkApiRateLimit('finnhub')) {
        return await this.fetchFromFinnhub(symbol, finnhubKey);
      }
    } catch (error) {
      console.error('Finnhub failed:', error);
    }

    return null;
  }

  private async fetchFromYahoo(symbol: string): Promise<MarketData> {
    const yahooSymbol = this.convertToYahooSymbol(symbol);
    const url = `https://query1.finance.yahoo.com/v8/finance/chart/${yahooSymbol}`;
    
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    });
    
    if (!response.ok) {
      throw new Error(`Yahoo Finance API error: ${response.status}`);
    }
    
    const data = await response.json();
    const result = data.chart.result[0];
    const meta = result.meta;
    const quote = result.indicators.quote[0];
    
    return {
      symbol: symbol,
      price: meta.regularMarketPrice || quote.close[quote.close.length - 1],
      change: meta.regularMarketPrice - meta.previousClose,
      changePercent: ((meta.regularMarketPrice - meta.previousClose) / meta.previousClose) * 100,
      volume: meta.regularMarketVolume || 0,
      timestamp: new Date(),
      source: 'Yahoo Finance'
    };
  }

  private async fetchFromAlphaVantage(symbol: string, apiKey: string): Promise<MarketData> {
    const url = `https://www.alphavantage.co/query?function=GLOBAL_QUOTE&symbol=${symbol}&apikey=${apiKey}`;
    
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`Alpha Vantage API error: ${response.status}`);
    }
    
    const data = await response.json();
    const quote = data['Global Quote'];
    
    if (!quote) {
      throw new Error('Invalid response from Alpha Vantage');
    }
    
    return {
      symbol: symbol,
      price: parseFloat(quote['05. price']),
      change: parseFloat(quote['09. change']),
      changePercent: parseFloat(quote['10. change percent'].replace('%', '')),
      volume: parseInt(quote['06. volume']),
      timestamp: new Date(),
      source: 'Alpha Vantage'
    };
  }

  private async fetchFromFinnhub(symbol: string, apiKey: string): Promise<MarketData> {
    const url = `https://finnhub.io/api/v1/quote?symbol=${symbol}&token=${apiKey}`;
    
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`Finnhub API error: ${response.status}`);
    }
    
    const data = await response.json();
    
    return {
      symbol: symbol,
      price: data.c,
      change: data.d,
      changePercent: data.dp,
      volume: 0, // Finnhub doesn't provide volume in quote endpoint
      timestamp: new Date(),
      source: 'Finnhub'
    };
  }

  private convertToYahooSymbol(symbol: string): string {
    // Convert common Indian stock symbols to Yahoo Finance format
    const indianStocks: Record<string, string> = {
      'RELIANCE': 'RELIANCE.NS',
      'TCS': 'TCS.NS',
      'HDFCBANK': 'HDFCBANK.NS',
      'INFY': 'INFY.NS',
      'HDFC': 'HDFC.NS',
      'ICICIBANK': 'ICICIBANK.NS',
      'KOTAKBANK': 'KOTAKBANK.NS',
      'HINDUNILVR': 'HINDUNILVR.NS',
      'SBIN': 'SBIN.NS',
      'BHARTIARTL': 'BHARTIARTL.NS'
    };
    
    return indianStocks[symbol.toUpperCase()] || `${symbol}.NS`;
  }

  async getHistoricalData(symbol: string, period: string = '1M'): Promise<HistoricalData | null> {
    if (!checkRateLimit(`historical_${symbol}`, 5, 300000)) { // 5 requests per 5 minutes per symbol
      throw new Error('Rate limit exceeded for historical data');
    }

    try {
      return await this.fetchHistoricalFromYahoo(symbol, period);
    } catch (error) {
      console.error('Failed to fetch historical data:', error);
      return null;
    }
  }

  private async fetchHistoricalFromYahoo(symbol: string, period: string): Promise<HistoricalData> {
    const yahooSymbol = this.convertToYahooSymbol(symbol);
    const url = `https://query1.finance.yahoo.com/v8/finance/chart/${yahooSymbol}?range=${period}&interval=1d`;
    
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`Yahoo Finance API error: ${response.status}`);
    }
    
    const data = await response.json();
    const result = data.chart.result[0];
    const timestamps = result.timestamp;
    const quote = result.indicators.quote[0];
    
    const historicalData = timestamps.map((timestamp: number, index: number) => ({
      date: new Date(timestamp * 1000).toISOString().split('T')[0],
      open: quote.open[index] || 0,
      high: quote.high[index] || 0,
      low: quote.low[index] || 0,
      close: quote.close[index] || 0,
      volume: quote.volume[index] || 0
    }));
    
    return {
      symbol: symbol,
      data: historicalData,
      source: 'Yahoo Finance'
    };
  }

  async testConnection(): Promise<boolean> {
    try {
      const testData = await this.getMarketData(['RELIANCE']);
      return testData.length > 0;
    } catch (error) {
      console.error('Connection test failed:', error);
      return false;
    }
  }

  // Cleanup method for security
  async clearAllApiKeys(): Promise<void> {
    try {
      await secureStorage.removeSecureItem('api_key_alpha_vantage');
      await secureStorage.removeSecureItem('api_key_finnhub');
      await secureStorage.removeSecureItem('api_key_iex_cloud');
      this.availableSources = ['Yahoo Finance'];
    } catch (error) {
      console.error('Failed to clear API keys:', error);
    }
  }
}

export const marketDataService = new MarketDataService();

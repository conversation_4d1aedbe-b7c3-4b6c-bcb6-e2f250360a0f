import { MarketData } from './MarketDataService';

export interface StreamData {
  symbol: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  timestamp: Date;
  bid?: number;
  ask?: number;
  lastTrade?: number;
}

export interface NewsItem {
  id: string;
  title: string;
  summary: string;
  sentiment: 'positive' | 'negative' | 'neutral';
  sentimentScore: number;
  source: string;
  timestamp: Date;
  symbols: string[];
}

class RealTimeDataStream {
  private wsConnection: WebSocket | null = null;
  private subscribers: Map<string, Set<(data: StreamData) => void>> = new Map();
  private newsSubscribers: Set<(news: NewsItem) => void> = new Set();
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private isConnecting = false;

  async connect(): Promise<boolean> {
    if (this.isConnecting || this.wsConnection?.readyState === WebSocket.OPEN) {
      return true;
    }

    this.isConnecting = true;

    try {
      // Using a mock WebSocket for demo - in production, use real financial data provider
      await this.simulateRealTimeData();
      this.isConnecting = false;
      return true;
    } catch (error) {
      console.error('Failed to connect to real-time data stream:', error);
      this.isConnecting = false;
      this.scheduleReconnect();
      return false;
    }
  }

  private async simulateRealTimeData(): Promise<void> {
    // Simulate real-time price updates for Indian stocks
    const symbols = ['RELIANCE', 'TCS', 'HDFCBANK', 'INFY', 'HDFC'];
    const basePrices: Record<string, number> = {
      'RELIANCE': 2850,
      'TCS': 4100,
      'HDFCBANK': 1650,
      'INFY': 1820,
      'HDFC': 2750
    };

    // Start price simulation
    setInterval(() => {
      symbols.forEach(symbol => {
        const basePrice = basePrices[symbol];
        const volatility = 0.02; // 2% volatility
        const change = (Math.random() - 0.5) * 2 * volatility * basePrice;
        const newPrice = basePrice + change;
        const changePercent = (change / basePrice) * 100;

        const streamData: StreamData = {
          symbol,
          price: Math.round(newPrice * 100) / 100,
          change: Math.round(change * 100) / 100,
          changePercent: Math.round(changePercent * 100) / 100,
          volume: Math.floor(Math.random() * 1000000),
          timestamp: new Date(),
          bid: newPrice - 0.05,
          ask: newPrice + 0.05,
          lastTrade: newPrice
        };

        // Update base price for next iteration
        basePrices[symbol] = newPrice;

        // Notify subscribers
        this.notifySubscribers(symbol, streamData);
      });
    }, 2000); // Update every 2 seconds

    // Simulate news feed
    this.simulateNewsFeed();
  }

  private simulateNewsFeed(): void {
    const newsTemplates = [
      {
        title: "Strong Q3 earnings reported",
        summary: "Company beats analyst expectations with strong revenue growth",
        sentiment: 'positive' as const,
        sentimentScore: 0.8
      },
      {
        title: "Market volatility increases amid global concerns",
        summary: "Trading volumes spike as investors react to international developments",
        sentiment: 'negative' as const,
        sentimentScore: -0.6
      },
      {
        title: "New partnership announced",
        summary: "Strategic alliance expected to boost market position",
        sentiment: 'positive' as const,
        sentimentScore: 0.7
      },
      {
        title: "Regulatory approval received",
        summary: "Key regulatory milestone achieved for expansion plans",
        sentiment: 'positive' as const,
        sentimentScore: 0.75
      }
    ];

    setInterval(() => {
      if (Math.random() > 0.7) { // 30% chance of news every interval
        const template = newsTemplates[Math.floor(Math.random() * newsTemplates.length)];
        const symbols = ['RELIANCE', 'TCS', 'HDFCBANK'];
        const randomSymbol = symbols[Math.floor(Math.random() * symbols.length)];

        const newsItem: NewsItem = {
          id: `news_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          title: `${randomSymbol}: ${template.title}`,
          summary: template.summary,
          sentiment: template.sentiment,
          sentimentScore: template.sentimentScore,
          source: 'Market News API',
          timestamp: new Date(),
          symbols: [randomSymbol]
        };

        this.notifyNewsSubscribers(newsItem);
      }
    }, 15000); // Check for news every 15 seconds
  }

  private notifySubscribers(symbol: string, data: StreamData): void {
    const symbolSubscribers = this.subscribers.get(symbol);
    if (symbolSubscribers) {
      symbolSubscribers.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('Error in stream subscriber callback:', error);
        }
      });
    }
  }

  private notifyNewsSubscribers(news: NewsItem): void {
    this.newsSubscribers.forEach(callback => {
      try {
        callback(news);
      } catch (error) {
        console.error('Error in news subscriber callback:', error);
      }
    });
  }

  subscribe(symbol: string, callback: (data: StreamData) => void): () => void {
    if (!this.subscribers.has(symbol)) {
      this.subscribers.set(symbol, new Set());
    }
    
    this.subscribers.get(symbol)!.add(callback);

    // Auto-connect if not already connected
    if (!this.wsConnection || this.wsConnection.readyState !== WebSocket.OPEN) {
      this.connect();
    }

    // Return unsubscribe function
    return () => {
      const symbolSubscribers = this.subscribers.get(symbol);
      if (symbolSubscribers) {
        symbolSubscribers.delete(callback);
        if (symbolSubscribers.size === 0) {
          this.subscribers.delete(symbol);
        }
      }
    };
  }

  subscribeToNews(callback: (news: NewsItem) => void): () => void {
    this.newsSubscribers.add(callback);

    // Auto-connect if not already connected
    if (!this.wsConnection || this.wsConnection.readyState !== WebSocket.OPEN) {
      this.connect();
    }

    // Return unsubscribe function
    return () => {
      this.newsSubscribers.delete(callback);
    };
  }

  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached');
      return;
    }

    const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);
    this.reconnectAttempts++;

    setTimeout(() => {
      console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
      this.connect();
    }, delay);
  }

  disconnect(): void {
    if (this.wsConnection) {
      this.wsConnection.close();
      this.wsConnection = null;
    }
    this.subscribers.clear();
    this.newsSubscribers.clear();
    this.reconnectAttempts = 0;
  }

  isConnected(): boolean {
    return this.wsConnection?.readyState === WebSocket.OPEN;
  }

  getSubscribedSymbols(): string[] {
    return Array.from(this.subscribers.keys());
  }
}

export const realTimeDataStream = new RealTimeDataStream();

import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Mail, Lock, User, Phone, AlertTriangle } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { authLoginSchema, authSignupSchema, sanitizeString, sanitizeEmail, generateSessionToken } from '@/lib/security';
import { secureStorage } from '@/services/SecureStorage';
import { useToast } from '@/hooks/use-toast';

interface AuthModalProps {
  open: boolean;
  onClose: () => void;
  onAuthenticated: (user: { email: string; name: string }) => void;
}

const AuthModal = ({ open, onClose, onAuthenticated }: AuthModalProps) => {
  const [loginForm, setLoginForm] = useState({ email: '', password: '' });
  const [signupForm, setSignupForm] = useState({ 
    name: '', 
    email: '', 
    phone: '', 
    password: '', 
    confirmPassword: '' 
  });
  const [loginErrors, setLoginErrors] = useState<Record<string, string>>({});
  const [signupErrors, setSignupErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setLoginErrors({});

    try {
      // Sanitize inputs
      const sanitizedData = {
        email: sanitizeEmail(loginForm.email),
        password: loginForm.password // Don't sanitize password as it may contain special chars
      };

      // Validate inputs
      const validation = authLoginSchema.safeParse(sanitizedData);
      if (!validation.success) {
        const errors: Record<string, string> = {};
        validation.error.errors.forEach((error) => {
          if (error.path[0]) {
            errors[error.path[0].toString()] = error.message;
          }
        });
        setLoginErrors(errors);
        return;
      }

      // Simulate authentication with secure session
      const sessionToken = generateSessionToken();
      const userData = { 
        email: sanitizedData.email, 
        name: sanitizedData.email.split('@')[0],
        sessionToken 
      };

      // Store session securely (expires in 24 hours)
      await secureStorage.setSecureItem('user_session', JSON.stringify(userData), true, 24 * 60 * 60 * 1000);
      await secureStorage.setSecureItem(`session_${sessionToken}`, JSON.stringify({
        expires: Date.now() + (24 * 60 * 60 * 1000),
        user: userData
      }), true);

      onAuthenticated({ email: userData.email, name: userData.name });
      toast({
        title: "Login Successful",
        description: "Welcome back!",
      });
      onClose();
    } catch (error) {
      toast({
        title: "Login Failed",
        description: "Please check your credentials and try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSignup = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setSignupErrors({});

    try {
      // Sanitize inputs
      const sanitizedData = {
        name: sanitizeString(signupForm.name),
        email: sanitizeEmail(signupForm.email),
        phone: sanitizeString(signupForm.phone),
        password: signupForm.password,
        confirmPassword: signupForm.confirmPassword
      };

      // Validate inputs
      const validation = authSignupSchema.safeParse(sanitizedData);
      if (!validation.success) {
        const errors: Record<string, string> = {};
        validation.error.errors.forEach((error) => {
          if (error.path[0]) {
            errors[error.path[0].toString()] = error.message;
          }
        });
        setSignupErrors(errors);
        return;
      }

      // Simulate user registration with secure session
      const sessionToken = generateSessionToken();
      const userData = { 
        email: sanitizedData.email, 
        name: sanitizedData.name,
        sessionToken 
      };

      // Store session securely
      await secureStorage.setSecureItem('user_session', JSON.stringify(userData), true, 24 * 60 * 60 * 1000);
      await secureStorage.setSecureItem(`session_${sessionToken}`, JSON.stringify({
        expires: Date.now() + (24 * 60 * 60 * 1000),
        user: userData
      }), true);

      onAuthenticated({ email: userData.email, name: userData.name });
      toast({
        title: "Account Created",
        description: "Welcome to Alpha Trade Flow AI!",
      });
      onClose();
    } catch (error) {
      toast({
        title: "Registration Failed",
        description: "Please try again later.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md bg-slate-900 border-slate-700 text-white">
        <DialogHeader>
          <DialogTitle className="text-center text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
            Alpha Trade Flow AI
          </DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="login" className="w-full">
          <TabsList className="grid w-full grid-cols-2 bg-slate-800">
            <TabsTrigger value="login" className="data-[state=active]:bg-blue-600">Login</TabsTrigger>
            <TabsTrigger value="signup" className="data-[state=active]:bg-blue-600">Sign Up</TabsTrigger>
          </TabsList>

          <TabsContent value="login" className="mt-6">
            <Card className="bg-transparent border-none shadow-none">
              <CardHeader className="pb-4">
                <CardTitle className="text-center text-lg">Welcome Back</CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleLogin} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="email" className="text-slate-300">Email</Label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                      <Input
                        id="email"
                        type="email"
                        placeholder="Enter your email"
                        value={loginForm.email}
                        onChange={(e) => setLoginForm({ ...loginForm, email: e.target.value })}
                        className="pl-10 bg-slate-800 border-slate-600 text-white"
                        required
                      />
                    </div>
                    {loginErrors.email && (
                      <Alert className="border-red-500/20 bg-red-900/10">
                        <AlertTriangle className="h-4 w-4 text-red-400" />
                        <AlertDescription className="text-red-300 text-sm">
                          {loginErrors.email}
                        </AlertDescription>
                      </Alert>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="password" className="text-slate-300">Password</Label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                      <Input
                        id="password"
                        type="password"
                        placeholder="Enter your password"
                        value={loginForm.password}
                        onChange={(e) => setLoginForm({ ...loginForm, password: e.target.value })}
                        className="pl-10 bg-slate-800 border-slate-600 text-white"
                        required
                      />
                    </div>
                    {loginErrors.password && (
                      <Alert className="border-red-500/20 bg-red-900/10">
                        <AlertTriangle className="h-4 w-4 text-red-400" />
                        <AlertDescription className="text-red-300 text-sm">
                          {loginErrors.password}
                        </AlertDescription>
                      </Alert>
                    )}
                  </div>
                  <Button 
                    type="submit" 
                    disabled={isLoading}
                    className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                  >
                    {isLoading ? 'Signing In...' : 'Sign In'}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="signup" className="mt-6">
            <Card className="bg-transparent border-none shadow-none">
              <CardHeader className="pb-4">
                <CardTitle className="text-center text-lg">Create Account</CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSignup} className="space-y-4">
                  {/* Name field with validation */}
                  <div className="space-y-2">
                    <Label htmlFor="name" className="text-slate-300">Full Name</Label>
                    <div className="relative">
                      <User className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                      <Input
                        id="name"
                        type="text"
                        placeholder="Enter your full name"
                        value={signupForm.name}
                        onChange={(e) => setSignupForm({ ...signupForm, name: e.target.value })}
                        className="pl-10 bg-slate-800 border-slate-600 text-white"
                        required
                      />
                    </div>
                    {signupErrors.name && (
                      <Alert className="border-red-500/20 bg-red-900/10">
                        <AlertTriangle className="h-4 w-4 text-red-400" />
                        <AlertDescription className="text-red-300 text-sm">
                          {signupErrors.name}
                        </AlertDescription>
                      </Alert>
                    )}
                  </div>

                  {/* Email field with validation */}
                  <div className="space-y-2">
                    <Label htmlFor="signup-email" className="text-slate-300">Email</Label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                      <Input
                        id="signup-email"
                        type="email"
                        placeholder="Enter your email"
                        value={signupForm.email}
                        onChange={(e) => setSignupForm({ ...signupForm, email: e.target.value })}
                        className="pl-10 bg-slate-800 border-slate-600 text-white"
                        required
                      />
                    </div>
                    {signupErrors.email && (
                      <Alert className="border-red-500/20 bg-red-900/10">
                        <AlertTriangle className="h-4 w-4 text-red-400" />
                        <AlertDescription className="text-red-300 text-sm">
                          {signupErrors.email}
                        </AlertDescription>
                      </Alert>
                    )}
                  </div>

                  {/* Phone field with validation */}
                  <div className="space-y-2">
                    <Label htmlFor="phone" className="text-slate-300">Phone Number</Label>
                    <div className="relative">
                      <Phone className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                      <Input
                        id="phone"
                        type="tel"
                        placeholder="Enter your phone number"
                        value={signupForm.phone}
                        onChange={(e) => setSignupForm({ ...signupForm, phone: e.target.value })}
                        className="pl-10 bg-slate-800 border-slate-600 text-white"
                        required
                      />
                    </div>
                    {signupErrors.phone && (
                      <Alert className="border-red-500/20 bg-red-900/10">
                        <AlertTriangle className="h-4 w-4 text-red-400" />
                        <AlertDescription className="text-red-300 text-sm">
                          {signupErrors.phone}
                        </AlertDescription>
                      </Alert>
                    )}
                  </div>

                  {/* Password fields with validation */}
                  <div className="space-y-2">
                    <Label htmlFor="signup-password" className="text-slate-300">Password</Label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                      <Input
                        id="signup-password"
                        type="password"
                        placeholder="Create a strong password"
                        value={signupForm.password}
                        onChange={(e) => setSignupForm({ ...signupForm, password: e.target.value })}
                        className="pl-10 bg-slate-800 border-slate-600 text-white"
                        required
                      />
                    </div>
                    {signupErrors.password && (
                      <Alert className="border-red-500/20 bg-red-900/10">
                        <AlertTriangle className="h-4 w-4 text-red-400" />
                        <AlertDescription className="text-red-300 text-sm">
                          {signupErrors.password}
                        </AlertDescription>
                      </Alert>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="confirm-password" className="text-slate-300">Confirm Password</Label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                      <Input
                        id="confirm-password"
                        type="password"
                        placeholder="Confirm your password"
                        value={signupForm.confirmPassword}
                        onChange={(e) => setSignupForm({ ...signupForm, confirmPassword: e.target.value })}
                        className="pl-10 bg-slate-800 border-slate-600 text-white"
                        required
                      />
                    </div>
                    {signupErrors.confirmPassword && (
                      <Alert className="border-red-500/20 bg-red-900/10">
                        <AlertTriangle className="h-4 w-4 text-red-400" />
                        <AlertDescription className="text-red-300 text-sm">
                          {signupErrors.confirmPassword}
                        </AlertDescription>
                      </Alert>
                    )}
                  </div>

                  <Button 
                    type="submit" 
                    disabled={isLoading}
                    className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                  >
                    {isLoading ? 'Creating Account...' : 'Create Account'}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <div className="text-center text-sm text-slate-400 mt-4">
          By continuing, you agree to our Terms of Service and Privacy Policy
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AuthModal;

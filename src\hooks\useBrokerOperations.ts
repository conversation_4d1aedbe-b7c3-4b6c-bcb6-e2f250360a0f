
import { useCallback } from 'react';
import { LiveBroker } from '@/services/LiveBroker';
import { BrokerAccount } from '@/types/broker';

interface UseBrokerOperationsProps {
  broker: LiveBroker;
  isConnected: boolean;
  setBrokerAccount: (account: BrokerAccount | null) => void;
}

export const useBrokerOperations = ({ broker, isConnected, setBrokerAccount }: UseBrokerOperationsProps) => {
  const getCurrentPositions = useCallback(async () => {
    if (!broker || !isConnected) return [];
    
    try {
      const result = await broker.getPositions();
      return result.success ? result.data : [];
    } catch (error) {
      console.error('Error getting live positions:', error);
      return [];
    }
  }, [broker, isConnected]);

  const getCurrentOrders = useCallback(async () => {
    if (!broker || !isConnected) return [];
    
    try {
      const result = await broker.getOrders();
      return result.success ? result.data : [];
    } catch (error) {
      console.error('Error getting live orders:', error);
      return [];
    }
  }, [broker, isConnected]);

  const cancelOrder = useCallback(async (orderId: string) => {
    if (!broker || !isConnected) return false;
    
    try {
      const result = await broker.cancelOrder(orderId);
      if (result.success) {
        const accountResult = await broker.refreshAccount();
        if (accountResult.success) {
          setBrokerAccount(accountResult.data);
        }
      }
      return result.success;
    } catch (error) {
      console.error('Error cancelling live order:', error);
      return false;
    }
  }, [broker, isConnected, setBrokerAccount]);

  const closePosition = useCallback(async (symbol: string, quantity?: number) => {
    if (!broker || !isConnected) return false;
    
    try {
      const result = await broker.closePosition(symbol, quantity);
      if (result.success) {
        const accountResult = await broker.refreshAccount();
        if (accountResult.success) {
          setBrokerAccount(accountResult.data);
        }
      }
      return result.success;
    } catch (error) {
      console.error('Error closing live position:', error);
      return false;
    }
  }, [broker, isConnected, setBrokerAccount]);

  return {
    getCurrentPositions,
    getCurrentOrders,
    cancelOrder,
    closePosition
  };
};

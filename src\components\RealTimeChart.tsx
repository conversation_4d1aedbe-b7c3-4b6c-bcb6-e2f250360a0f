import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON>Chart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { TrendingUp, TrendingDown, Activity, Wifi, WifiOff } from 'lucide-react';
import { useRealTimeData, useRealTimeNews } from '@/hooks/useRealTimeData';

interface ChartDataPoint {
  time: string;
  price: number;
  volume: number;
}

interface RealTimeChartProps {
  symbols?: string[];
  selectedSymbol?: string;
  onSymbolSelect?: (symbol: string) => void;
}

const RealTimeChart: React.FC<RealTimeChartProps> = ({ 
  symbols = ['RELIANCE', 'TCS', 'HDFCBANK'],
  selectedSymbol = 'RELIANCE',
  onSymbolSelect
}) => {
  const { data, isConnected, connectionError } = useRealTimeData(symbols);
  const { news, getNewsBySymbol } = useRealTimeNews();
  const [chartData, setChartData] = useState<Record<string, ChartDataPoint[]>>({});

  // Update chart data when real-time data arrives
  useEffect(() => {
    Object.entries(data).forEach(([symbol, streamData]) => {
      const newDataPoint: ChartDataPoint = {
        time: new Date(streamData.timestamp).toLocaleTimeString(),
        price: streamData.price,
        volume: streamData.volume
      };

      setChartData(prev => {
        const existingData = prev[symbol] || [];
        const updatedData = [...existingData, newDataPoint].slice(-50); // Keep last 50 points
        
        return {
          ...prev,
          [symbol]: updatedData
        };
      });
    });
  }, [data]);

  const currentData = data[selectedSymbol];
  const currentChartData = chartData[selectedSymbol] || [];
  const symbolNews = getNewsBySymbol(selectedSymbol);

  const getChangeColor = (change: number) => {
    if (change > 0) return 'text-green-400';
    if (change < 0) return 'text-red-400';
    return 'text-slate-400';
  };

  const getChangeIcon = (change: number) => {
    if (change > 0) return <TrendingUp className="w-4 h-4" />;
    if (change < 0) return <TrendingDown className="w-4 h-4" />;
    return <Activity className="w-4 h-4" />;
  };

  return (
    <div className="space-y-4">
      {/* Symbol Selector and Connection Status */}
      <Card className="bg-slate-800/50 border-slate-700">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Activity className="w-5 h-5 text-blue-400" />
              Real-Time Market Data
            </CardTitle>
            <div className="flex items-center gap-2">
              {isConnected ? (
                <Badge variant="outline" className="bg-green-900/20 text-green-400 border-green-500">
                  <Wifi className="w-3 h-3 mr-1" />
                  Live
                </Badge>
              ) : (
                <Badge variant="outline" className="bg-red-900/20 text-red-400 border-red-500">
                  <WifiOff className="w-3 h-3 mr-1" />
                  Disconnected
                </Badge>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {symbols.map(symbol => (
              <Button
                key={symbol}
                variant={selectedSymbol === symbol ? "default" : "outline"}
                size="sm"
                onClick={() => onSymbolSelect?.(symbol)}
                className={selectedSymbol === symbol ? 
                  "bg-blue-600 hover:bg-blue-700" : 
                  "border-slate-600 hover:bg-slate-700"
                }
              >
                {symbol}
                {data[symbol] && (
                  <span className={`ml-2 ${getChangeColor(data[symbol].change)}`}>
                    {data[symbol].changePercent > 0 ? '+' : ''}{data[symbol].changePercent.toFixed(2)}%
                  </span>
                )}
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Price Chart */}
      <Card className="bg-slate-800/50 border-slate-700">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                {selectedSymbol}
                {currentData && getChangeIcon(currentData.change)}
              </CardTitle>
              {currentData && (
                <div className="flex items-center gap-4 mt-2">
                  <span className="text-2xl font-bold">₹{currentData.price.toFixed(2)}</span>
                  <span className={`text-lg ${getChangeColor(currentData.change)}`}>
                    {currentData.change > 0 ? '+' : ''}₹{currentData.change.toFixed(2)}
                  </span>
                  <span className={`text-lg ${getChangeColor(currentData.changePercent)}`}>
                    ({currentData.changePercent > 0 ? '+' : ''}{currentData.changePercent.toFixed(2)}%)
                  </span>
                </div>
              )}
            </div>
            {currentData && (
              <div className="text-right text-sm text-slate-400">
                <div>Volume: {currentData.volume.toLocaleString()}</div>
                <div>Last Update: {new Date(currentData.timestamp).toLocaleTimeString()}</div>
              </div>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={currentChartData}>
                <defs>
                  <linearGradient id="priceGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#3B82F6" stopOpacity={0.3}/>
                    <stop offset="95%" stopColor="#3B82F6" stopOpacity={0}/>
                  </linearGradient>
                </defs>
                <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                <XAxis 
                  dataKey="time" 
                  stroke="#9CA3AF"
                  tick={{ fontSize: 12 }}
                />
                <YAxis 
                  stroke="#9CA3AF"
                  tick={{ fontSize: 12 }}
                  domain={['dataMin - 5', 'dataMax + 5']}
                />
                <Tooltip 
                  contentStyle={{ 
                    backgroundColor: '#1F2937', 
                    border: '1px solid #374151',
                    borderRadius: '8px',
                    color: '#F3F4F6'
                  }}
                  formatter={(value: number) => [`₹${value.toFixed(2)}`, 'Price']}
                />
                <Line 
                  type="monotone" 
                  dataKey="price" 
                  stroke="#3B82F6" 
                  strokeWidth={2}
                  dot={false}
                  fill="url(#priceGradient)"
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      {/* Real-time News Feed */}
      <Card className="bg-slate-800/50 border-slate-700">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="w-5 h-5 text-orange-400" />
            Latest News - {selectedSymbol}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3 max-h-60 overflow-y-auto">
            {symbolNews.length > 0 ? (
              symbolNews.slice(0, 5).map(newsItem => (
                <div key={newsItem.id} className="border-l-2 border-blue-500 pl-3 py-2">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm font-medium text-slate-200">{newsItem.title}</span>
                    <Badge 
                      variant="outline"
                      className={
                        newsItem.sentiment === 'positive' ? 'bg-green-900/20 text-green-400 border-green-500' :
                        newsItem.sentiment === 'negative' ? 'bg-red-900/20 text-red-400 border-red-500' :
                        'bg-slate-900/20 text-slate-400 border-slate-500'
                      }
                    >
                      {newsItem.sentiment}
                    </Badge>
                  </div>
                  <p className="text-xs text-slate-400">{newsItem.summary}</p>
                  <div className="flex justify-between items-center mt-1">
                    <span className="text-xs text-slate-500">{newsItem.source}</span>
                    <span className="text-xs text-slate-500">
                      {new Date(newsItem.timestamp).toLocaleTimeString()}
                    </span>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center text-slate-400 py-4">
                No recent news for {selectedSymbol}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {connectionError && (
        <Card className="bg-red-900/20 border-red-500">
          <CardContent className="p-4">
            <p className="text-red-400 text-sm">
              Connection Error: {connectionError}
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default RealTimeChart;

import { MarketSignal, TechnicalIndicator, OHLCV } from '@/types/signals';
import { NewsArticle, SocialMediaPost, MLPrediction, AISignalConfig, MarketDataPoint } from '@/types/ai';
import { SentimentAnalyzer } from './SentimentAnalyzer';
import { MLPredictor } from './MLPredictor';
import { SignalGenerator } from './SignalGenerator';

export class AISignalGenerator extends SignalGenerator {
  private aiConfig: AISignalConfig;

  constructor(config: any, aiConfig: AISignalConfig) {
    super(config);
    this.aiConfig = aiConfig;
  }

  /**
   * Generate AI-enhanced trading signals
   */
  async generateAISignals(
    symbol: string,
    ohlcvData: OHLCV[],
    newsArticles: NewsArticle[],
    socialPosts: SocialMediaPost[],
    timeframe: '1m' | '5m' | '15m' | '1h' | '4h' | '1d' = '15m'
  ): Promise<MarketSignal[]> {
    try {
      console.log(`Generating AI signals for ${symbol}...`);
      
      // Get base technical signals
      const technicalSignals = await this.generateSignals(symbol, ohlcvData, timeframe);
      
      // Generate sentiment analysis
      const newsSentiment = await SentimentAnalyzer.analyzeNews(
        newsArticles.filter(article => article.symbols.includes(symbol))
      );
      
      const socialSentiment = await SentimentAnalyzer.analyzeSocialMedia(
        socialPosts.filter(post => post.symbols.includes(symbol))
      );
      
      // Convert timeframe for ML prediction (ML model only supports longer timeframes)
      const mlTimeframe = this.convertToMLTimeframe(timeframe);
      
      // Generate ML predictions
      const marketData = this.generateMockMarketData(symbol, newsSentiment, socialSentiment);
      const mlPrediction = await MLPredictor.generatePrediction(symbol, ohlcvData, marketData, mlTimeframe);
      
      // Enhance signals with AI insights
      const enhancedSignals = await this.enhanceSignalsWithAI(
        technicalSignals,
        newsSentiment,
        socialSentiment,
        mlPrediction
      );
      
      console.log(`Generated ${enhancedSignals.length} AI-enhanced signals for ${symbol}`);
      return enhancedSignals;
      
    } catch (error) {
      console.error(`Error generating AI signals for ${symbol}:`, error);
      return [];
    }
  }

  /**
   * Convert timeframe to ML-compatible format
   */
  private convertToMLTimeframe(timeframe: '1m' | '5m' | '15m' | '1h' | '4h' | '1d'): '1h' | '4h' | '1d' {
    switch (timeframe) {
      case '1m':
      case '5m':
      case '15m':
        return '1h'; // Convert short timeframes to 1h
      case '1h':
        return '1h';
      case '4h':
        return '4h';
      case '1d':
        return '1d';
      default:
        return '1h';
    }
  }

  /**
   * Enhance technical signals with AI insights
   */
  private async enhanceSignalsWithAI(
    technicalSignals: MarketSignal[],
    newsSentiment: any,
    socialSentiment: any,
    mlPrediction: MLPrediction
  ): Promise<MarketSignal[]> {
    const enhancedSignals: MarketSignal[] = [];

    for (const signal of technicalSignals) {
      // Calculate AI sentiment score
      const sentimentScore = this.calculateSentimentScore(newsSentiment, socialSentiment);
      
      // Combine technical and AI scores
      const aiConfidence = this.combineAIFactors(
        signal.confidence,
        sentimentScore,
        mlPrediction.prediction.confidence
      );
      
      // Create AI-enhanced signal
      const enhancedSignal: MarketSignal = {
        ...signal,
        type: 'AI_PREDICTION',
        confidence: aiConfidence.confidence,
        strength: aiConfidence.strength,
        reasoning: this.generateAIReasoning(signal, newsSentiment, socialSentiment, mlPrediction),
        indicators: [
          ...signal.indicators,
          this.createSentimentIndicator(newsSentiment, 'News'),
          this.createSentimentIndicator(socialSentiment, 'Social'),
          this.createMLIndicator(mlPrediction)
        ]
      };

      // Only include signals that meet AI confidence threshold
      if (enhancedSignal.confidence >= this.aiConfig.minSentimentScore * 100) {
        enhancedSignals.push(enhancedSignal);
      }
    }

    return enhancedSignals;
  }

  /**
   * Calculate sentiment score
   */
  private calculateSentimentScore(newsSentiment: any, socialSentiment: any): number {
    const newsWeight = this.aiConfig.newsWeight;
    const socialWeight = this.aiConfig.socialWeight;
    
    const combinedSentiment = (
      newsSentiment.polarity * newsWeight +
      socialSentiment.polarity * socialWeight
    ) / (newsWeight + socialWeight);
    
    return combinedSentiment;
  }

  /**
   * Combine AI factors for final confidence
   */
  private combineAIFactors(
    technicalConfidence: number,
    sentimentScore: number,
    mlConfidence: number
  ): { confidence: number; strength: number } {
    const weights = {
      technical: 1 - this.aiConfig.sentimentWeight - this.aiConfig.mlWeight,
      sentiment: this.aiConfig.sentimentWeight,
      ml: this.aiConfig.mlWeight
    };
    
    const sentimentConfidence = Math.abs(sentimentScore) * 100;
    const mlConfidencePercent = mlConfidence * 100;
    
    const combinedConfidence = (
      technicalConfidence * weights.technical +
      sentimentConfidence * weights.sentiment +
      mlConfidencePercent * weights.ml
    );
    
    const strength = Math.min(95, combinedConfidence * 1.1);
    
    return {
      confidence: Math.min(95, combinedConfidence),
      strength: Math.max(50, strength)
    };
  }

  /**
   * Generate AI-enhanced reasoning
   */
  private generateAIReasoning(
    signal: MarketSignal,
    newsSentiment: any,
    socialSentiment: any,
    mlPrediction: MLPrediction
  ): string {
    const reasons = [signal.reasoning];
    
    if (Math.abs(newsSentiment.polarity) > 0.3) {
      reasons.push(`News sentiment: ${newsSentiment.label.toLowerCase()} (${(newsSentiment.polarity * 100).toFixed(1)}%)`);
    }
    
    if (Math.abs(socialSentiment.polarity) > 0.3) {
      reasons.push(`Social sentiment: ${socialSentiment.label.toLowerCase()} (${(socialSentiment.polarity * 100).toFixed(1)}%)`);
    }
    
    if (mlPrediction.prediction.confidence > 0.6) {
      reasons.push(`ML model predicts ${mlPrediction.prediction.direction.toLowerCase()} with ${(mlPrediction.prediction.confidence * 100).toFixed(1)}% confidence`);
    }
    
    return reasons.join(', ');
  }

  /**
   * Create sentiment indicator
   */
  private createSentimentIndicator(sentiment: any, type: string): TechnicalIndicator {
    let signal: 'BUY' | 'SELL' | 'HOLD' = 'HOLD';
    
    if (sentiment.polarity > 0.2) signal = 'BUY';
    else if (sentiment.polarity < -0.2) signal = 'SELL';
    
    return {
      name: `${type} Sentiment`,
      value: sentiment.polarity,
      signal,
      strength: Math.min(90, Math.abs(sentiment.polarity) * 100 + 50),
      timestamp: new Date()
    };
  }

  /**
   * Create ML prediction indicator
   */
  private createMLIndicator(mlPrediction: MLPrediction): TechnicalIndicator {
    let signal: 'BUY' | 'SELL' | 'HOLD' = 'HOLD';
    
    if (mlPrediction.prediction.direction === 'UP') signal = 'BUY';
    else if (mlPrediction.prediction.direction === 'DOWN') signal = 'SELL';
    
    return {
      name: 'ML Prediction',
      value: mlPrediction.prediction.confidence,
      signal,
      strength: Math.min(90, mlPrediction.prediction.confidence * 100),
      timestamp: new Date()
    };
  }

  /**
   * Generate mock market data for demo
   */
  private generateMockMarketData(symbol: string, newsSentiment: any, socialSentiment: any): MarketDataPoint[] {
    return [{
      symbol,
      timestamp: new Date(),
      price: 15000 + Math.random() * 1000,
      volume: Math.floor(Math.random() * 1000000) + 500000,
      volatility: Math.random() * 0.5,
      sentiment: {
        polarity: (newsSentiment.polarity + socialSentiment.polarity) / 2,
        confidence: (newsSentiment.confidence + socialSentiment.confidence) / 2,
        label: newsSentiment.label,
        subjectivity: (newsSentiment.subjectivity + socialSentiment.subjectivity) / 2
      },
      newsCount: Math.floor(Math.random() * 10) + 1,
      socialMentions: Math.floor(Math.random() * 100) + 10
    }];
  }
}

import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import GoogleCloudSetup from './GoogleCloudSetup';
import { VoiceCommandInterface } from './VoiceCommandInterface';
import { Cloud, Mic, MessageSquare, Languages, Image } from 'lucide-react';

interface GoogleCloudDashboardProps {
  onVoiceCommand?: (command: string, params: any) => void;
}

export const GoogleCloudDashboard: React.FC<GoogleCloudDashboardProps> = ({ onVoiceCommand }) => {
  const handleVoiceCommand = (command: string, params: any) => {
    console.log('Voice command received:', command, params);
    
    // Handle different voice commands
    switch (command) {
      case 'SHOW_CHART':
        // Navigate to chart for symbol
        break;
      case 'SET_ALERT':
        // Set price alert
        break;
      case 'BUY':
      case 'SELL':
        // Execute trade command
        break;
      case 'GET_NEWS':
        // Show news for symbol
        break;
      case 'SHOW_PORTFOLIO':
        // Show portfolio
        break;
      case 'MARKET_STATUS':
        // Show market status
        break;
      default:
        console.log('Unknown command:', command);
    }
    
    onVoiceCommand?.(command, params);
  };

  return (
    <div className="space-y-6">
      <Card className="backdrop-blur-md bg-card/50 border-primary/20">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Cloud className="h-5 w-5" />
            Google Cloud AI Services
          </CardTitle>
        </CardHeader>
        
        <CardContent>
          <Tabs defaultValue="setup" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="setup">Setup</TabsTrigger>
              <TabsTrigger value="voice">Voice Commands</TabsTrigger>
              <TabsTrigger value="sentiment">Sentiment AI</TabsTrigger>
              <TabsTrigger value="translation">Translation</TabsTrigger>
            </TabsList>
            
            <TabsContent value="setup">
              <GoogleCloudSetup />
            </TabsContent>
            
            <TabsContent value="voice">
              <VoiceCommandInterface onCommand={handleVoiceCommand} />
            </TabsContent>
            
            <TabsContent value="sentiment" className="space-y-4">
              <Card className="p-4">
                <div className="flex items-center gap-2 mb-3">
                  <MessageSquare className="h-5 w-5" />
                  <h3 className="font-semibold">Enhanced Sentiment Analysis</h3>
                </div>
                <p className="text-sm text-muted-foreground mb-4">
                  Using Google Cloud Natural Language API for advanced sentiment analysis of market news and social media.
                </p>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Accuracy Improvement</span>
                    <span className="font-medium text-green-600">+35%</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Language Support</span>
                    <span className="font-medium">100+ Languages</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Real-time Processing</span>
                    <span className="font-medium text-blue-600">Active</span>
                  </div>
                </div>
              </Card>
            </TabsContent>
            
            <TabsContent value="translation" className="space-y-4">
              <Card className="p-4">
                <div className="flex items-center gap-2 mb-3">
                  <Languages className="h-5 w-5" />
                  <h3 className="font-semibold">Multi-Language Support</h3>
                </div>
                <p className="text-sm text-muted-foreground mb-4">
                  Translate global financial news and analysis into your preferred language.
                </p>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Supported Languages</span>
                    <span className="font-medium">100+</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Translation Quality</span>
                    <span className="font-medium text-green-600">Professional</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Real-time Translation</span>
                    <span className="font-medium text-blue-600">Active</span>
                  </div>
                </div>
              </Card>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Bot, 
  Workflow, 
  Zap, 
  Brain, 
  Globe, 
  Settings, 
  Play,
  Pause,
  RotateCcw,
  Activity,
  CheckCircle,
  AlertTriangle,
  ExternalLink
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import AIWorkflowTestRunner from './AIWorkflowTestRunner';

const AIWorkflowHub = () => {
  const [activeWorkflows, setActiveWorkflows] = useState(0);
  const [agentConfigs, setAgentConfigs] = useState({
    huggingface: '',
    openaiCompatible: 'https://api.openai.com/v1', // Sample URL
    groq: 'https://api.groq.com/openai/v1', // Sample URL
    localLLM: 'http://localhost:11434/v1' // Sample Ollama URL
  });
  const [isRunning, setIsRunning] = useState(false);
  const { toast } = useToast();

  const freeAIProviders = [
    {
      name: 'Hugging Face',
      type: 'Transformers',
      apiUrl: 'https://api-inference.huggingface.co/models/',
      features: ['Text Generation', 'Sentiment Analysis', 'Classification'],
      status: 'active',
      docs: 'https://huggingface.co/docs/api-inference/quicktour'
    },
    {
      name: 'Groq',
      type: 'LLM Inference',
      apiUrl: 'https://api.groq.com/openai/v1',
      features: ['Fast LLM Inference', 'Chat Completion', 'Function Calling'],
      status: 'active',
      docs: 'https://console.groq.com/docs/quickstart'
    },
    {
      name: 'Ollama (Local)',
      type: 'Local LLM',
      apiUrl: 'http://localhost:11434/v1',
      features: ['Privacy', 'No Rate Limits', 'Offline'],
      status: 'pending',
      docs: 'https://ollama.ai/library'
    },
    {
      name: 'OpenRouter',
      type: 'Multi-Model',
      apiUrl: 'https://openrouter.ai/api/v1',
      features: ['Multiple Models', 'Free Tier', 'API Gateway'],
      status: 'active',
      docs: 'https://openrouter.ai/docs'
    }
  ];

  const workflowTemplates = [
    {
      id: 'market-analysis',
      name: 'Market Analysis Agent',
      description: 'AI agent that analyzes market trends and generates insights',
      triggers: ['Price Change', 'Volume Spike', 'News Event'],
      actions: ['Generate Report', 'Send Alert', 'Update Dashboard'],
      complexity: 'medium'
    },
    {
      id: 'risk-monitor',
      name: 'Risk Monitoring Agent',
      description: 'Continuous risk assessment and portfolio protection',
      triggers: ['Portfolio Loss', 'Volatility Spike', 'Correlation Change'],
      actions: ['Adjust Position', 'Send Warning', 'Generate Risk Report'],
      complexity: 'high'
    },
    {
      id: 'sentiment-tracker',
      name: 'Sentiment Analysis Agent',
      description: 'Tracks market sentiment from news and social media',
      triggers: ['News Update', 'Social Media Mention', 'Earnings Call'],
      actions: ['Update Sentiment Score', 'Generate Summary', 'Trigger Alert'],
      complexity: 'low'
    },
    {
      id: 'auto-trader',
      name: 'Automated Trading Agent',
      description: 'Executes trades based on AI signals and risk parameters',
      triggers: ['AI Signal', 'Technical Indicator', 'Time Schedule'],
      actions: ['Place Order', 'Modify Position', 'Update Strategy'],
      complexity: 'high'
    }
  ];

  const startWorkflow = async (workflowId: string) => {
    setIsRunning(true);
    try {
      // Simulate workflow initialization
      await new Promise(resolve => setTimeout(resolve, 1000));
      setActiveWorkflows(prev => prev + 1);
      toast({
        title: "Workflow Started",
        description: `${workflowId} is now running`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to start workflow",
        variant: "destructive",
      });
    } finally {
      setIsRunning(false);
    }
  };

  const testConnection = async (provider: string, url: string) => {
    try {
      toast({
        title: "Testing Connection",
        description: `Connecting to ${provider}...`,
      });
      
      // Sample test request - in real implementation, this would test the actual API
      console.log(`Testing ${provider} at ${url}`);
      
      // Simulate connection test
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      toast({
        title: "Connection Successful",
        description: `${provider} is ready to use`,
      });
    } catch (error) {
      toast({
        title: "Connection Failed",
        description: `Could not connect to ${provider}`,
        variant: "destructive",
      });
    }
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">AI Workflow Hub</h1>
          <p className="text-slate-400">Automate your trading with AI agents and workflows</p>
        </div>
        <div className="flex items-center gap-4">
          <Badge className="bg-green-900/20 text-green-400 border-green-500">
            <Activity className="w-3 h-3 mr-1" />
            {activeWorkflows} Active
          </Badge>
          <Button 
            onClick={() => setActiveWorkflows(0)}
            variant="outline"
            size="sm"
          >
            <RotateCcw className="w-4 h-4 mr-2" />
            Reset All
          </Button>
        </div>
      </div>

      <Tabs defaultValue="test-runner" className="space-y-4">
        <TabsList className="grid w-full grid-cols-5 bg-slate-700/50">
          <TabsTrigger value="test-runner">Test Runner</TabsTrigger>
          <TabsTrigger value="providers">AI Providers</TabsTrigger>
          <TabsTrigger value="workflows">Workflows</TabsTrigger>
          <TabsTrigger value="agents">Agents</TabsTrigger>
          <TabsTrigger value="monitoring">Monitoring</TabsTrigger>
        </TabsList>

        <TabsContent value="test-runner" className="space-y-6">
          <AIWorkflowTestRunner />
        </TabsContent>

        <TabsContent value="providers" className="space-y-4">
          <Alert className="border-blue-500/20 bg-blue-900/10">
            <Brain className="h-4 w-4 text-blue-400" />
            <AlertDescription className="text-blue-300">
              <strong>Free AI Resources:</strong> All providers below offer free tiers or are completely open source.
              <br />Setup instructions and API keys can be found in their documentation.
            </AlertDescription>
          </Alert>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {freeAIProviders.map((provider) => (
              <Card key={provider.name} className="bg-slate-800/50 border-slate-700">
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Globe className="w-5 h-5 text-blue-400" />
                      {provider.name}
                    </div>
                    <Badge 
                      className={
                        provider.status === 'active' 
                          ? 'bg-green-900/20 text-green-400 border-green-500'
                          : 'bg-yellow-900/20 text-yellow-400 border-yellow-500'
                      }
                    >
                      {provider.status}
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label className="text-slate-300">API Endpoint</Label>
                    <Input
                      value={provider.apiUrl}
                      readOnly
                      className="bg-slate-700/30 border-slate-600 text-slate-300"
                    />
                  </div>
                  
                  <div>
                    <Label className="text-slate-300">Features</Label>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {provider.features.map((feature) => (
                        <Badge key={feature} variant="secondary" className="text-xs">
                          {feature}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      onClick={() => testConnection(provider.name, provider.apiUrl)}
                      disabled={isRunning}
                    >
                      <CheckCircle className="w-3 h-3 mr-1" />
                      Test
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => window.open(provider.docs, '_blank')}
                    >
                      <ExternalLink className="w-3 h-3 mr-1" />
                      Docs
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="workflows" className="space-y-4">
          <Alert className="border-purple-500/20 bg-purple-900/10">
            <Workflow className="h-4 w-4 text-purple-400" />
            <AlertDescription className="text-purple-300">
              <strong>Workflow Templates:</strong> Pre-built automation workflows that you can customize and deploy.
              <br />Each workflow uses AI agents to automate specific trading tasks.
            </AlertDescription>
          </Alert>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {workflowTemplates.map((workflow) => (
              <Card key={workflow.id} className="bg-slate-800/50 border-slate-700">
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Bot className="w-5 h-5 text-purple-400" />
                      {workflow.name}
                    </div>
                    <Badge 
                      className={
                        workflow.complexity === 'low' 
                          ? 'bg-green-900/20 text-green-400 border-green-500'
                          : workflow.complexity === 'medium'
                          ? 'bg-yellow-900/20 text-yellow-400 border-yellow-500'
                          : 'bg-red-900/20 text-red-400 border-red-500'
                      }
                    >
                      {workflow.complexity}
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-slate-300 text-sm">{workflow.description}</p>
                  
                  <div>
                    <Label className="text-slate-300">Triggers</Label>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {workflow.triggers.map((trigger) => (
                        <Badge key={trigger} variant="outline" className="text-xs">
                          <Zap className="w-2 h-2 mr-1" />
                          {trigger}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div>
                    <Label className="text-slate-300">Actions</Label>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {workflow.actions.map((action) => (
                        <Badge key={action} variant="secondary" className="text-xs">
                          <Settings className="w-2 h-2 mr-1" />
                          {action}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      onClick={() => startWorkflow(workflow.name)}
                      disabled={isRunning}
                      className="bg-purple-600 hover:bg-purple-700"
                    >
                      <Play className="w-3 h-3 mr-1" />
                      Deploy
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                    >
                      <Settings className="w-3 h-3 mr-1" />
                      Configure
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="agents" className="space-y-4">
          <Alert className="border-green-500/20 bg-green-900/10">
            <Bot className="h-4 w-4 text-green-400" />
            <AlertDescription className="text-green-300">
              <strong>AI Agents:</strong> Autonomous agents that can make decisions and take actions based on market conditions.
              <br />Each agent can be configured with different AI models and parameters.
            </AlertDescription>
          </Alert>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <CardTitle className="text-green-400">Data Collection Agent</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-slate-300 text-sm mb-4">
                  Continuously gathers market data, news, and social sentiment
                </p>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-slate-400">Status:</span>
                    <Badge className="bg-green-900/20 text-green-400 border-green-500">Running</Badge>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-slate-400">Data Points:</span>
                    <span className="text-white">1,247</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-slate-400">Last Update:</span>
                    <span className="text-white">2 min ago</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <CardTitle className="text-blue-400">Analysis Agent</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-slate-300 text-sm mb-4">
                  Processes data using AI models to generate trading insights
                </p>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-slate-400">Status:</span>
                    <Badge className="bg-blue-900/20 text-blue-400 border-blue-500">Processing</Badge>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-slate-400">Model:</span>
                    <span className="text-white">Llama-3.1-70B</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-slate-400">Confidence:</span>
                    <span className="text-white">87%</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <CardTitle className="text-purple-400">Execution Agent</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-slate-300 text-sm mb-4">
                  Executes trades and manages risk based on AI recommendations
                </p>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-slate-400">Status:</span>
                    <Badge className="bg-yellow-900/20 text-yellow-400 border-yellow-500">Standby</Badge>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-slate-400">Orders:</span>
                    <span className="text-white">3 Pending</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-slate-400">Success Rate:</span>
                    <span className="text-white">94%</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="monitoring" className="space-y-4">
          <Alert className="border-orange-500/20 bg-orange-900/10">
            <Activity className="h-4 w-4 text-orange-400" />
            <AlertDescription className="text-orange-300">
              <strong>Real-time Monitoring:</strong> Track all AI agents and workflows in real-time.
              <br />Get alerts when agents need attention or workflows complete tasks.
            </AlertDescription>
          </Alert>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card className="bg-slate-800/50 border-slate-700">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-slate-400 text-sm">Active Agents</p>
                    <p className="text-2xl font-bold text-white">12</p>
                  </div>
                  <Bot className="w-8 h-8 text-blue-400" />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-slate-800/50 border-slate-700">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-slate-400 text-sm">API Calls Today</p>
                    <p className="text-2xl font-bold text-white">2,847</p>
                  </div>
                  <Globe className="w-8 h-8 text-green-400" />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-slate-800/50 border-slate-700">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-slate-400 text-sm">Success Rate</p>
                    <p className="text-2xl font-bold text-white">96%</p>
                  </div>
                  <CheckCircle className="w-8 h-8 text-green-400" />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-slate-800/50 border-slate-700">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-slate-400 text-sm">Alerts</p>
                    <p className="text-2xl font-bold text-white">3</p>
                  </div>
                  <AlertTriangle className="w-8 h-8 text-yellow-400" />
                </div>
              </CardContent>
            </Card>
          </div>

          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {[
                  { time: '2 min ago', agent: 'Market Analysis', action: 'Generated bullish signal for RELIANCE', status: 'success' },
                  { time: '5 min ago', agent: 'Risk Monitor', action: 'Adjusted position size for TCS', status: 'warning' },
                  { time: '12 min ago', agent: 'Sentiment Tracker', action: 'Updated sentiment score: Positive (0.78)', status: 'info' },
                  { time: '18 min ago', agent: 'Auto Trader', action: 'Executed buy order for HDFC Bank', status: 'success' },
                ].map((activity, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-slate-700/30 rounded-lg">
                    <div>
                      <p className="text-white font-medium">{activity.agent}</p>
                      <p className="text-slate-400 text-sm">{activity.action}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-slate-400 text-xs">{activity.time}</p>
                      <Badge 
                        className={
                          activity.status === 'success' 
                            ? 'bg-green-900/20 text-green-400 border-green-500'
                            : activity.status === 'warning'
                            ? 'bg-yellow-900/20 text-yellow-400 border-yellow-500'
                            : 'bg-blue-900/20 text-blue-400 border-blue-500'
                        }
                      >
                        {activity.status}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AIWorkflowHub;
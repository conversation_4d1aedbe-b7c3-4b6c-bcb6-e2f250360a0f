
import { AccountInfo, RiskParameters, PositionSizeResult } from '@/types/trading';

export class PositionSizingCalculator {
  private account: AccountInfo;
  private riskParams: RiskParameters;

  constructor(account: AccountInfo, riskParams: RiskParameters) {
    this.account = account;
    this.riskParams = riskParams;
  }

  /**
   * Calculate position size using fixed percentage risk method
   */
  calculateFixedPercentageSize(
    entryPrice: number,
    stopLoss: number,
    riskPercentage?: number
  ): PositionSizeResult {
    const risk = riskPercentage || this.riskParams.maxRiskPerTrade;
    const riskAmount = (this.account.totalBalance * risk) / 100;
    const riskPerShare = Math.abs(entryPrice - stopLoss);
    
    if (riskPerShare === 0) {
      throw new Error('Stop loss cannot be equal to entry price');
    }

    const quantity = Math.floor(riskAmount / riskPerShare);
    const positionValue = quantity * entryPrice;
    const actualRiskAmount = quantity * riskPerShare;

    return {
      quantity,
      riskAmount: actualRiskAmount,
      positionValue,
      riskPercentage: (actualRiskAmount / this.account.totalBalance) * 100
    };
  }

  /**
   * Calculate position size using Kelly Criterion
   */
  calculateKellySize(
    entryPrice: number,
    stopLoss: number,
    winRate: number,
    avgWin: number,
    avgLoss: number
  ): PositionSizeResult {
    // Kelly % = (bp - q) / b
    // where b = odds received (avg win / avg loss), p = win rate, q = lose rate
    const b = avgWin / avgLoss;
    const p = winRate / 100;
    const q = 1 - p;
    
    const kellyPercentage = Math.max(0, Math.min(25, (b * p - q) / b * 100)); // Cap at 25%
    
    return this.calculateFixedPercentageSize(entryPrice, stopLoss, kellyPercentage);
  }

  /**
   * Calculate position size based on volatility (ATR)
   */
  calculateVolatilityAdjustedSize(
    entryPrice: number,
    stopLoss: number,
    atr: number,
    volatilityMultiplier: number = 1.5
  ): PositionSizeResult {
    // Adjust risk based on volatility
    const baseRisk = this.riskParams.maxRiskPerTrade;
    const volatilityAdjustment = Math.min(2, Math.max(0.5, atr / (entryPrice * 0.02)));
    const adjustedRisk = baseRisk / (volatilityAdjustment * volatilityMultiplier);
    
    return this.calculateFixedPercentageSize(entryPrice, stopLoss, adjustedRisk);
  }

  /**
   * Get maximum allowed position value for a symbol considering exposure limits
   */
  getMaxPositionValue(symbol: string, sector: string): number {
    const maxSingleStock = (this.account.totalBalance * this.riskParams.maxSingleStockExposure) / 100;
    const maxSector = (this.account.totalBalance * this.riskParams.maxSectorExposure) / 100;
    
    // Calculate current sector exposure
    const currentSectorExposure = this.account.positions
      .filter(pos => pos.sector === sector)
      .reduce((sum, pos) => sum + pos.marketValue, 0);
    
    const availableSectorCapacity = maxSector - currentSectorExposure;
    
    return Math.min(maxSingleStock, availableSectorCapacity, this.account.availableBalance);
  }
}

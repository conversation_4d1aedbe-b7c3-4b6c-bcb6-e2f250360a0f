import { secureStorage } from './SecureStorage';

export interface AIProvider {
  name: string;
  baseUrl: string;
  apiKey?: string;
  model: string;
  type: 'openai-compatible' | 'huggingface' | 'custom';
}

export interface AgentConfig {
  id: string;
  name: string;
  provider: AIProvider;
  systemPrompt: string;
  triggers: string[];
  actions: string[];
  isActive: boolean;
  settings: Record<string, any>;
}

export interface WorkflowStep {
  id: string;
  name: string;
  type: 'data-collection' | 'analysis' | 'decision' | 'action';
  agentId?: string;
  config: Record<string, any>;
  dependencies: string[];
}

export interface Workflow {
  id: string;
  name: string;
  description: string;
  steps: WorkflowStep[];
  schedule?: string;
  isActive: boolean;
  lastRun?: Date;
  nextRun?: Date;
}

class AIAgentService {
  private agents: Map<string, AgentConfig> = new Map();
  private workflows: Map<string, Workflow> = new Map();
  private executionLog: Array<{ timestamp: Date; agentId: string; action: string; result: any }> = [];

  // Free AI Provider Templates
  getDefaultProviders(): AIProvider[] {
    return [
      {
        name: 'Groq',
        baseUrl: 'https://api.groq.com/openai/v1',
        model: 'llama3-70b-8192',
        type: 'openai-compatible'
      },
      {
        name: 'Hugging Face',
        baseUrl: 'https://api-inference.huggingface.co/models',
        model: 'microsoft/DialoGPT-large',
        type: 'huggingface'
      },
      {
        name: 'OpenRouter',
        baseUrl: 'https://openrouter.ai/api/v1',
        model: 'meta-llama/llama-3.1-8b-instruct:free',
        type: 'openai-compatible'
      },
      {
        name: 'Ollama (Local)',
        baseUrl: 'http://localhost:11434/v1',
        model: 'llama3.1:8b',
        type: 'openai-compatible'
      },
      {
        name: 'Together AI',
        baseUrl: 'https://api.together.xyz/v1',
        model: 'meta-llama/Llama-3-8b-chat-hf',
        type: 'openai-compatible'
      }
    ];
  }

  // Agent Templates
  getAgentTemplates(): Partial<AgentConfig>[] {
    return [
      {
        name: 'Market Sentiment Analyzer',
        systemPrompt: `You are a market sentiment analysis agent. Analyze the provided market data, news, and social media sentiment to determine overall market sentiment.

Your tasks:
1. Analyze news headlines and content for sentiment
2. Process social media mentions and discussions
3. Evaluate market price movements and volume
4. Generate sentiment score (-1 to 1, where -1 is very bearish, 1 is very bullish)
5. Provide reasoning for your sentiment analysis

Output format:
{
  "sentiment_score": number,
  "confidence": number,
  "reasoning": "string",
  "key_factors": ["array", "of", "factors"],
  "recommendation": "buy|sell|hold"
}`,
        triggers: ['news_update', 'price_change', 'volume_spike'],
        actions: ['update_sentiment', 'send_alert', 'generate_report']
      },
      {
        name: 'Risk Management Agent',
        systemPrompt: `You are a risk management agent for trading portfolios. Your primary goal is to protect capital and manage risk exposure.

Your responsibilities:
1. Monitor portfolio exposure and concentration
2. Calculate position sizes based on risk parameters
3. Detect risk threshold breaches
4. Recommend risk mitigation actions
5. Monitor correlation and diversification

Risk Parameters:
- Max position size: 5% of portfolio
- Max sector exposure: 20%
- Max daily loss limit: 2%
- VaR confidence level: 95%

Output format:
{
  "risk_level": "low|medium|high|critical",
  "current_exposure": number,
  "recommendations": ["array", "of", "actions"],
  "position_adjustments": {},
  "alerts": ["any", "immediate", "concerns"]
}`,
        triggers: ['portfolio_update', 'price_volatility', 'correlation_change'],
        actions: ['adjust_position', 'send_warning', 'halt_trading']
      },
      {
        name: 'Technical Analysis Agent',
        systemPrompt: `You are a technical analysis agent specializing in chart pattern recognition and indicator analysis.

Your expertise includes:
1. Candlestick pattern recognition
2. Support/resistance level identification
3. Technical indicator analysis (RSI, MACD, Bollinger Bands)
4. Trend analysis and momentum assessment
5. Entry/exit point recommendations

Analysis Framework:
- Multiple timeframe analysis (1h, 4h, 1d)
- Confluence of indicators for signal strength
- Risk/reward ratio assessment
- Volume confirmation

Output format:
{
  "signal": "strong_buy|buy|neutral|sell|strong_sell",
  "confidence": number,
  "entry_price": number,
  "stop_loss": number,
  "targets": [number, number, number],
  "timeframe": "string",
  "indicators": {},
  "patterns": ["identified", "patterns"]
}`,
        triggers: ['price_update', 'indicator_signal', 'pattern_detected'],
        actions: ['generate_signal', 'update_analysis', 'place_alert']
      }
    ];
  }

  // Workflow Templates
  getWorkflowTemplates(): Partial<Workflow>[] {
    return [
      {
        name: 'Complete Market Analysis',
        description: 'End-to-end market analysis workflow combining multiple AI agents',
        steps: [
          {
            id: 'data-collection',
            name: 'Collect Market Data',
            type: 'data-collection',
            config: {
              sources: ['yahoo_finance', 'news_api', 'social_media'],
              symbols: ['RELIANCE', 'TCS', 'HDFCBANK', 'INFY']
            },
            dependencies: []
          },
          {
            id: 'sentiment-analysis',
            name: 'Analyze Market Sentiment',
            type: 'analysis',
            agentId: 'sentiment-analyzer',
            config: {
              data_sources: ['news', 'social_media', 'price_action']
            },
            dependencies: ['data-collection']
          },
          {
            id: 'technical-analysis',
            name: 'Technical Analysis',
            type: 'analysis',
            agentId: 'technical-analyzer',
            config: {
              timeframes: ['1h', '4h', '1d'],
              indicators: ['rsi', 'macd', 'bollinger_bands']
            },
            dependencies: ['data-collection']
          },
          {
            id: 'risk-assessment',
            name: 'Risk Assessment',
            type: 'analysis',
            agentId: 'risk-manager',
            config: {
              portfolio_data: true,
              risk_metrics: ['var', 'sharpe_ratio', 'max_drawdown']
            },
            dependencies: ['sentiment-analysis', 'technical-analysis']
          },
          {
            id: 'decision',
            name: 'Generate Trading Decision',
            type: 'decision',
            config: {
              decision_weights: {
                sentiment: 0.3,
                technical: 0.5,
                risk: 0.2
              }
            },
            dependencies: ['risk-assessment']
          }
        ]
      },
      {
        name: 'Automated Risk Monitoring',
        description: 'Continuous portfolio risk monitoring and adjustment',
        steps: [
          {
            id: 'portfolio-scan',
            name: 'Scan Portfolio Positions',
            type: 'data-collection',
            config: {
              frequency: '5m',
              metrics: ['pnl', 'exposure', 'correlation']
            },
            dependencies: []
          },
          {
            id: 'risk-calculation',
            name: 'Calculate Risk Metrics',
            type: 'analysis',
            agentId: 'risk-manager',
            config: {
              metrics: ['var', 'exposure', 'concentration']
            },
            dependencies: ['portfolio-scan']
          },
          {
            id: 'threshold-check',
            name: 'Check Risk Thresholds',
            type: 'decision',
            config: {
              thresholds: {
                max_portfolio_loss: 0.02,
                max_position_size: 0.05,
                max_sector_exposure: 0.20
              }
            },
            dependencies: ['risk-calculation']
          },
          {
            id: 'auto-adjust',
            name: 'Auto-adjust Positions',
            type: 'action',
            config: {
              actions: ['reduce_position', 'send_alert', 'halt_trading']
            },
            dependencies: ['threshold-check']
          }
        ]
      }
    ];
  }

  // API Communication Methods
  async callAgent(agentId: string, input: any): Promise<any> {
    const agent = this.agents.get(agentId);
    if (!agent || !agent.isActive) {
      throw new Error(`Agent ${agentId} not found or inactive`);
    }

    const provider = agent.provider;
    
    try {
      let response;
      
      if (provider.type === 'openai-compatible') {
        response = await this.callOpenAICompatible(provider, agent.systemPrompt, input);
      } else if (provider.type === 'huggingface') {
        response = await this.callHuggingFace(provider, input);
      } else {
        throw new Error(`Unsupported provider type: ${provider.type}`);
      }

      // Log execution
      this.executionLog.push({
        timestamp: new Date(),
        agentId,
        action: 'api_call',
        result: response
      });

      return response;
    } catch (error) {
      console.error(`Error calling agent ${agentId}:`, error);
      throw error;
    }
  }

  private async callOpenAICompatible(provider: AIProvider, systemPrompt: string, userInput: any): Promise<any> {
    const response = await fetch(`${provider.baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${provider.apiKey || 'demo-key'}`
      },
      body: JSON.stringify({
        model: provider.model,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: JSON.stringify(userInput) }
        ],
        temperature: 0.1,
        max_tokens: 2000
      })
    });

    if (!response.ok) {
      throw new Error(`API call failed: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data.choices[0].message.content;
  }

  private async callHuggingFace(provider: AIProvider, input: any): Promise<any> {
    const response = await fetch(`${provider.baseUrl}/${provider.model}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${provider.apiKey || 'demo-key'}`
      },
      body: JSON.stringify({
        inputs: JSON.stringify(input),
        parameters: {
          max_length: 1000,
          temperature: 0.1
        }
      })
    });

    if (!response.ok) {
      throw new Error(`Hugging Face API call failed: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  }

  // Agent Management
  async createAgent(config: Partial<AgentConfig>): Promise<string> {
    const id = `agent_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const fullConfig: AgentConfig = {
      id,
      name: config.name || 'Unnamed Agent',
      provider: config.provider || this.getDefaultProviders()[0],
      systemPrompt: config.systemPrompt || 'You are a helpful trading assistant.',
      triggers: config.triggers || [],
      actions: config.actions || [],
      isActive: config.isActive !== undefined ? config.isActive : true,
      settings: config.settings || {}
    };

    this.agents.set(id, fullConfig);
    
    // Save to secure storage
    await secureStorage.setSecureItem(`agent_${id}`, JSON.stringify(fullConfig));
    
    return id;
  }

  async executeWorkflow(workflowId: string): Promise<any> {
    const workflow = this.workflows.get(workflowId);
    if (!workflow || !workflow.isActive) {
      throw new Error(`Workflow ${workflowId} not found or inactive`);
    }

    const results: Record<string, any> = {};
    const executionOrder = this.resolveExecutionOrder(workflow.steps);

    for (const stepId of executionOrder) {
      const step = workflow.steps.find(s => s.id === stepId);
      if (!step) continue;

      try {
        const stepInput = this.prepareStepInput(step, results);
        const stepResult = await this.executeStep(step, stepInput);
        results[stepId] = stepResult;
      } catch (error) {
        console.error(`Error executing step ${stepId}:`, error);
        results[stepId] = { error: error.message };
      }
    }

    // Update workflow last run
    workflow.lastRun = new Date();
    
    return results;
  }

  private resolveExecutionOrder(steps: WorkflowStep[]): string[] {
    const resolved: string[] = [];
    const remaining = [...steps];

    while (remaining.length > 0) {
      const canExecute = remaining.filter(step => 
        step.dependencies.every(dep => resolved.includes(dep))
      );

      if (canExecute.length === 0) {
        throw new Error('Circular dependency detected in workflow');
      }

      const next = canExecute[0];
      resolved.push(next.id);
      remaining.splice(remaining.indexOf(next), 1);
    }

    return resolved;
  }

  private prepareStepInput(step: WorkflowStep, previousResults: Record<string, any>): any {
    const input = { ...step.config };
    
    // Add results from dependencies
    for (const depId of step.dependencies) {
      if (previousResults[depId]) {
        input[`${depId}_result`] = previousResults[depId];
      }
    }

    return input;
  }

  private async executeStep(step: WorkflowStep, input: any): Promise<any> {
    switch (step.type) {
      case 'data-collection':
        return this.executeDataCollection(step, input);
      case 'analysis':
        if (step.agentId) {
          return this.callAgent(step.agentId, input);
        }
        return this.executeAnalysis(step, input);
      case 'decision':
        return this.executeDecision(step, input);
      case 'action':
        return this.executeAction(step, input);
      default:
        throw new Error(`Unknown step type: ${step.type}`);
    }
  }

  private async executeDataCollection(step: WorkflowStep, input: any): Promise<any> {
    // Simulate data collection
    return {
      timestamp: new Date().toISOString(),
      data: `Collected data for ${JSON.stringify(input)}`,
      sources: input.sources || ['default']
    };
  }

  private async executeAnalysis(step: WorkflowStep, input: any): Promise<any> {
    // Simulate analysis
    return {
      timestamp: new Date().toISOString(),
      analysis: `Analysis result for ${JSON.stringify(input)}`,
      confidence: Math.random()
    };
  }

  private async executeDecision(step: WorkflowStep, input: any): Promise<any> {
    // Simulate decision making
    const decisions = ['buy', 'sell', 'hold'];
    return {
      timestamp: new Date().toISOString(),
      decision: decisions[Math.floor(Math.random() * decisions.length)],
      confidence: Math.random(),
      reasoning: 'Based on analysis results'
    };
  }

  private async executeAction(step: WorkflowStep, input: any): Promise<any> {
    // Simulate action execution
    return {
      timestamp: new Date().toISOString(),
      action: 'executed',
      details: input
    };
  }

  // Utility Methods
  getAgents(): AgentConfig[] {
    return Array.from(this.agents.values());
  }

  getWorkflows(): Workflow[] {
    return Array.from(this.workflows.values());
  }

  getExecutionLog(): Array<{ timestamp: Date; agentId: string; action: string; result: any }> {
    return this.executionLog.slice(-100); // Return last 100 entries
  }

  async saveApiKey(providerName: string, apiKey: string): Promise<void> {
    await secureStorage.setSecureItem(`ai_api_key_${providerName.toLowerCase()}`, apiKey, true);
  }

  async getApiKey(providerName: string): Promise<string | null> {
    return await secureStorage.getSecureItem(`ai_api_key_${providerName.toLowerCase()}`);
  }
}

export const aiAgentService = new AIAgentService();

import { Order, Trade, BrokerAccount, OrderRequest, BrokerResponse } from '@/types/broker';

export abstract class BrokerInterface {
  protected account: BrokerAccount;
  protected connected: boolean = false;

  constructor() {
    this.account = this.initializeAccount();
  }

  protected abstract initializeAccount(): BrokerAccount;

  // Account Management
  abstract connect(): Promise<BrokerResponse<boolean>>;
  abstract disconnect(): Promise<BrokerResponse<boolean>>;
  abstract getAccount(): Promise<BrokerResponse<BrokerAccount>>;
  abstract refreshAccount(): Promise<BrokerResponse<BrokerAccount>>;

  // Order Management
  abstract placeOrder(orderRequest: OrderRequest): Promise<BrokerResponse<Order>>;
  abstract cancelOrder(orderId: string): Promise<BrokerResponse<boolean>>;
  abstract modifyOrder(orderId: string, updates: Partial<OrderRequest>): Promise<BrokerResponse<Order>>;
  abstract getOrder(orderId: string): Promise<BrokerResponse<Order>>;
  abstract getOrders(symbol?: string): Promise<BrokerResponse<Order[]>>;

  // Position Management
  abstract getPositions(symbol?: string): Promise<BrokerResponse<any[]>>;
  abstract closePosition(symbol: string, quantity?: number): Promise<BrokerResponse<Order>>;

  // Market Data
  abstract getCurrentPrice(symbol: string): Promise<BrokerResponse<number>>;
  abstract getMarketData(symbol: string): Promise<BrokerResponse<any>>;

  // Trading History
  abstract getTrades(symbol?: string, limit?: number): Promise<BrokerResponse<Trade[]>>;

  // Utility Methods
  isConnected(): boolean {
    return this.connected;
  }

  getCurrentAccount(): BrokerAccount {
    return this.account;
  }
}

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Database, 
  Key, 
  CheckCircle, 
  AlertTriangle,
  ExternalLink,
  Info,
  Shield
} from 'lucide-react';
import { marketDataService } from '@/services/MarketDataService';
import { secureStorage } from '@/services/SecureStorage';
import { apiKeySchema, sanitizeString, checkRateLimit } from '@/lib/security';
import { useToast } from '@/hooks/use-toast';

const MarketDataSetup = () => {
  const [apiKeys, setApiKeys] = useState({
    alphaVantage: '',
    finnhub: '',
    iex: ''
  });
  const [savedKeys, setSavedKeys] = useState<string[]>([]);
  const [availableSources, setAvailableSources] = useState<string[]>([]);
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const { toast } = useToast();

  useEffect(() => {
    // Check which API keys are already saved securely
    checkSavedKeys();
    setAvailableSources(marketDataService.getAvailableSources());
  }, []);

  const checkSavedKeys = async () => {
    const saved = [];
    try {
      if (await secureStorage.getSecureItem('api_key_alpha_vantage')) saved.push('Alpha Vantage');
      if (await secureStorage.getSecureItem('api_key_finnhub')) saved.push('Finnhub');
      if (await secureStorage.getSecureItem('api_key_iex')) saved.push('IEX Cloud');
      setSavedKeys(saved);
    } catch (error) {
      console.error('Error checking saved keys:', error);
    }
  };

  const saveApiKey = async (service: string, key: string) => {
    const sanitizedKey = sanitizeString(key);
    
    // Rate limiting check
    if (!checkRateLimit(`api_key_save_${service}`, 5, 60000)) {
      toast({
        title: "Rate Limited",
        description: "Too many attempts. Please wait a minute.",
        variant: "destructive",
      });
      return;
    }

    // Validate API key
    const validation = apiKeySchema.safeParse({ 
      key: sanitizedKey, 
      service: service as 'ALPHA_VANTAGE' | 'FINNHUB' | 'IEX_CLOUD'
    });
    
    if (!validation.success) {
      setValidationErrors({ [service]: validation.error.errors[0].message });
      return;
    }

    setValidationErrors({});

    try {
      // Store API key securely with encryption
      await secureStorage.setSecureItem(`api_key_${service.toLowerCase()}`, sanitizedKey, true);
      
      // Update market data service
      marketDataService.saveApiKey(service, sanitizedKey);
      
      // Update local state
      const serviceName = service.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
      setSavedKeys(prev => [...prev.filter(k => k !== serviceName), serviceName]);
      setAvailableSources(marketDataService.getAvailableSources());
      
      // Clear input
      const serviceKey = service.toLowerCase().replace('_', '') as keyof typeof apiKeys;
      setApiKeys(prev => ({ ...prev, [serviceKey]: '' }));

      toast({
        title: "API Key Saved",
        description: `${serviceName} API key has been securely stored.`,
      });
    } catch (error) {
      toast({
        title: "Save Failed",
        description: "Failed to save API key securely. Please try again.",
        variant: "destructive",
      });
    }
  };

  const testConnection = async () => {
    if (!checkRateLimit('connection_test', 3, 60000)) {
      toast({
        title: "Rate Limited",
        description: "Too many connection tests. Please wait a minute.",
        variant: "destructive",
      });
      return;
    }

    setIsTestingConnection(true);
    setConnectionStatus('idle');
    
    try {
      const result = await marketDataService.testConnection();
      setConnectionStatus(result ? 'success' : 'error');
      
      if (result) {
        toast({
          title: "Connection Successful",
          description: "All data sources are working properly.",
        });
      } else {
        toast({
          title: "Connection Failed",
          description: "Some data sources may not be available.",
          variant: "destructive",
        });
      }
    } catch (error) {
      setConnectionStatus('error');
      toast({
        title: "Connection Error",
        description: "Failed to test connection. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsTestingConnection(false);
    }
  };

  const getSignupLinks = () => ({
    'Alpha Vantage': 'https://www.alphavantage.co/support/#api-key',
    'Finnhub': 'https://finnhub.io/register',
    'IEX Cloud': 'https://iexcloud.io/cloud-login#/register'
  });

  const links = getSignupLinks();

  return (
    <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="w-5 h-5 text-blue-400" />
          Market Data Setup
          <Badge className={availableSources.length > 0 ? 'bg-green-900/20 text-green-400 border-green-500' : 'bg-yellow-900/20 text-yellow-400 border-yellow-500'}>
            {availableSources.length} sources active
          </Badge>
          <Shield className="w-4 h-4 text-green-400" />
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="setup" className="space-y-4">
          <TabsList className="grid w-full grid-cols-3 bg-slate-700/50">
            <TabsTrigger value="setup">API Setup</TabsTrigger>
            <TabsTrigger value="free">Free Sources</TabsTrigger>
            <TabsTrigger value="status">Status</TabsTrigger>
          </TabsList>

          <TabsContent value="setup" className="space-y-4">
            <Alert className="border-blue-500/20 bg-blue-900/10">
              <Shield className="h-4 w-4 text-blue-400" />
              <AlertDescription className="text-blue-300">
                API keys are encrypted and stored securely in your browser. They never leave your device unencrypted.
              </AlertDescription>
            </Alert>

            <div className="space-y-4">
              {/* Alpha Vantage */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label className="text-slate-300">Alpha Vantage API Key</Label>
                  {savedKeys.includes('Alpha Vantage') ? (
                    <Badge className="bg-green-900/20 text-green-400 border-green-500">
                      <CheckCircle className="w-3 h-3 mr-1" />
                      Saved Securely
                    </Badge>
                  ) : (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => window.open(links['Alpha Vantage'], '_blank')}
                      className="text-xs"
                    >
                      Get Free Key <ExternalLink className="w-3 h-3 ml-1" />
                    </Button>
                  )}
                </div>
                <div className="flex gap-2">
                  <Input
                    type="password"
                    placeholder="Enter Alpha Vantage API key..."
                    value={apiKeys.alphaVantage}
                    onChange={(e) => setApiKeys(prev => ({ ...prev, alphaVantage: e.target.value }))}
                    className="bg-slate-700/30 border-slate-600"
                  />
                  <Button
                    onClick={() => saveApiKey('ALPHA_VANTAGE', apiKeys.alphaVantage)}
                    disabled={!apiKeys.alphaVantage.trim()}
                    size="sm"
                  >
                    Save
                  </Button>
                </div>
                {validationErrors.ALPHA_VANTAGE && (
                  <Alert className="border-red-500/20 bg-red-900/10">
                    <AlertTriangle className="h-4 w-4 text-red-400" />
                    <AlertDescription className="text-red-300 text-sm">
                      {validationErrors.ALPHA_VANTAGE}
                    </AlertDescription>
                  </Alert>
                )}
                <p className="text-xs text-slate-400">Free: 5 API requests per minute, 500 per day</p>
              </div>

              {/* Finnhub */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label className="text-slate-300">Finnhub API Key</Label>
                  {savedKeys.includes('Finnhub') ? (
                    <Badge className="bg-green-900/20 text-green-400 border-green-500">
                      <CheckCircle className="w-3 h-3 mr-1" />
                      Saved Securely
                    </Badge>
                  ) : (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => window.open(links['Finnhub'], '_blank')}
                      className="text-xs"
                    >
                      Get Free Key <ExternalLink className="w-3 h-3 ml-1" />
                    </Button>
                  )}
                </div>
                <div className="flex gap-2">
                  <Input
                    type="password"
                    placeholder="Enter Finnhub API key..."
                    value={apiKeys.finnhub}
                    onChange={(e) => setApiKeys(prev => ({ ...prev, finnhub: e.target.value }))}
                    className="bg-slate-700/30 border-slate-600"
                  />
                  <Button
                    onClick={() => saveApiKey('FINNHUB', apiKeys.finnhub)}
                    disabled={!apiKeys.finnhub.trim()}
                    size="sm"
                  >
                    Save
                  </Button>
                </div>
                {validationErrors.FINNHUB && (
                  <Alert className="border-red-500/20 bg-red-900/10">
                    <AlertTriangle className="h-4 w-4 text-red-400" />
                    <AlertDescription className="text-red-300 text-sm">
                      {validationErrors.FINNHUB}
                    </AlertDescription>
                  </Alert>
                )}
                <p className="text-xs text-slate-400">Free: 60 API calls per minute</p>
              </div>

              {/* IEX Cloud */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label className="text-slate-300">IEX Cloud API Key</Label>
                  {savedKeys.includes('IEX Cloud') ? (
                    <Badge className="bg-green-900/20 text-green-400 border-green-500">
                      <CheckCircle className="w-3 h-3 mr-1" />
                      Saved Securely
                    </Badge>
                  ) : (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => window.open(links['IEX Cloud'], '_blank')}
                      className="text-xs"
                    >
                      Get Free Key <ExternalLink className="w-3 h-3 ml-1" />
                    </Button>
                  )}
                </div>
                <div className="flex gap-2">
                  <Input
                    type="password"
                    placeholder="Enter IEX Cloud API key..."
                    value={apiKeys.iex}
                    onChange={(e) => setApiKeys(prev => ({ ...prev, iex: e.target.value }))}
                    className="bg-slate-700/30 border-slate-600"
                  />
                  <Button
                    onClick={() => saveApiKey('IEX_CLOUD', apiKeys.iex)}
                    disabled={!apiKeys.iex.trim()}
                    size="sm"
                  >
                    Save
                  </Button>
                </div>
                {validationErrors.IEX_CLOUD && (
                  <Alert className="border-red-500/20 bg-red-900/10">
                    <AlertTriangle className="h-4 w-4 text-red-400" />
                    <AlertDescription className="text-red-300 text-sm">
                      {validationErrors.IEX_CLOUD}
                    </AlertDescription>
                  </Alert>
                )}
                <p className="text-xs text-slate-400">Free: 50,000 core data credits per month</p>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="free" className="space-y-4">
            <div className="space-y-3">
              <h4 className="text-sm font-medium text-slate-300">Available Free Sources (No API Key Required)</h4>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between p-3 bg-slate-700/30 rounded-lg">
                  <div>
                    <div className="font-medium text-white">Yahoo Finance</div>
                    <div className="text-xs text-slate-400">Real-time quotes for Indian markets</div>
                  </div>
                  <Badge className="bg-green-900/20 text-green-400 border-green-500">
                    <CheckCircle className="w-3 h-3 mr-1" />
                    Active
                  </Badge>
                </div>

                <div className="flex items-center justify-between p-3 bg-slate-700/30 rounded-lg">
                  <div>
                    <div className="font-medium text-white">NSE Scraper</div>
                    <div className="text-xs text-slate-400">Direct from NSE (rate limited)</div>
                  </div>
                  <Badge className="bg-yellow-900/20 text-yellow-400 border-yellow-500">
                    <AlertTriangle className="w-3 h-3 mr-1" />
                    Limited
                  </Badge>
                </div>

                <div className="flex items-center justify-between p-3 bg-slate-700/30 rounded-lg">
                  <div>
                    <div className="font-medium text-white">BSE Scraper</div>
                    <div className="text-xs text-slate-400">Direct from BSE (experimental)</div>
                  </div>
                  <Badge className="bg-blue-900/20 text-blue-400 border-blue-500">
                    <Key className="w-3 h-3 mr-1" />
                    Beta
                  </Badge>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="status" className="space-y-4">
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Button
                  onClick={testConnection}
                  disabled={isTestingConnection}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  {isTestingConnection ? 'Testing...' : 'Test Connection'}
                </Button>
                
                {connectionStatus === 'success' && (
                  <Badge className="bg-green-900/20 text-green-400 border-green-500">
                    <CheckCircle className="w-3 h-3 mr-1" />
                    Connected
                  </Badge>
                )}
                
                {connectionStatus === 'error' && (
                  <Badge className="bg-red-900/20 text-red-400 border-red-500">
                    <AlertTriangle className="w-3 h-3 mr-1" />
                    Connection Failed
                  </Badge>
                )}
              </div>

              <div className="space-y-2">
                <h4 className="text-sm font-medium text-slate-300">Active Data Sources</h4>
                <div className="space-y-1">
                  {availableSources.length === 0 ? (
                    <p className="text-sm text-slate-400">No data sources available</p>
                  ) : (
                    availableSources.map((source) => (
                      <div key={source} className="flex items-center gap-2 text-sm">
                        <CheckCircle className="w-3 h-3 text-green-400" />
                        <span className="text-slate-300">{source}</span>
                      </div>
                    ))
                  )}
                </div>
              </div>

              <Alert className="border-slate-600 bg-slate-700/30">
                <Info className="h-4 w-4 text-slate-400" />
                <AlertDescription className="text-slate-300">
                  <strong>Recommended Setup:</strong>
                  <br />
                  1. Start with Yahoo Finance (already active)
                  <br />
                  2. Add Alpha Vantage for backup data
                  <br />
                  3. Add Finnhub for real-time WebSocket feeds
                </AlertDescription>
              </Alert>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default MarketDataSetup;

import React, { useState, useRef, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Brain, Mic, MicOff, Volume2, VolumeX, Camera, FileText, TrendingUp, AlertTriangle, DollarSign } from 'lucide-react';
import { GoogleCloudService } from '@/services/GoogleCloudService';
import { ElevenLabsService } from '@/services/ElevenLabsService';
import { useBrokerManager } from '@/hooks/useBrokerManager';
import { useToast } from '@/hooks/use-toast';
import { motion, AnimatePresence } from 'framer-motion';

interface ChatMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  data?: any;
}

interface TradeAnalysis {
  symbol: string;
  action: 'BUY' | 'SELL';
  quantity: number;
  price?: number;
  confidence: number;
  reasoning: string;
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
}

interface VoiceJournalEntry {
  id: string;
  transcript: string;
  summary: string;
  sentiment: string;
  timestamp: Date;
  tradingIdeas?: string[];
}

export const IntelligentTradingAssistant: React.FC = () => {
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [currentInput, setCurrentInput] = useState('');
  const [isListening, setIsListening] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [voiceJournal, setVoiceJournal] = useState<VoiceJournalEntry[]>([]);
  const [analysisResults, setAnalysisResults] = useState<TradeAnalysis[]>([]);
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);
  
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { broker, getCurrentPositions, getCurrentOrders } = useBrokerManager();
  const { toast } = useToast();

  // Initialize with welcome message
  useEffect(() => {
    const welcomeMessage: ChatMessage = {
      id: 'welcome',
      type: 'assistant',
      content: "Hello! I'm your intelligent trading assistant. I can help you with voice commands, chart analysis, trade execution, and market insights. How can I assist you today?",
      timestamp: new Date()
    };
    setChatMessages([welcomeMessage]);
    
    // Speak welcome message if ElevenLabs is configured
    if (ElevenLabsService.isConfigured()) {
      speakMessage(welcomeMessage.content);
    }
  }, []);

  const startVoiceRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];
      
      mediaRecorder.ondataavailable = (event) => {
        audioChunksRef.current.push(event.data);
      };
      
      mediaRecorder.onstop = async () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });
        await processVoiceInput(audioBlob);
        stream.getTracks().forEach(track => track.stop());
      };
      
      mediaRecorder.start();
      setIsListening(true);
      
      toast({
        title: "Listening",
        description: "Recording voice input...",
      });
      
    } catch (error) {
      console.error('Voice recording error:', error);
      toast({
        title: "Voice Error",
        description: "Could not access microphone",
        variant: "destructive"
      });
    }
  };

  const stopVoiceRecording = () => {
    if (mediaRecorderRef.current && isListening) {
      mediaRecorderRef.current.stop();
      setIsListening(false);
    }
  };

  const processVoiceInput = async (audioBlob: Blob) => {
    setIsProcessing(true);
    
    try {
      // Convert to base64 for Google Cloud Speech-to-Text
      const arrayBuffer = await audioBlob.arrayBuffer();
      const base64Audio = btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)));
      
      const transcript = await GoogleCloudService.speechToText(base64Audio);
      
      if (transcript) {
        // Add voice journal entry
        const journalEntry: VoiceJournalEntry = {
          id: Date.now().toString(),
          transcript,
          summary: await summarizeVoiceInput(transcript),
          sentiment: (await GoogleCloudService.analyzeSentiment(transcript)).label,
          timestamp: new Date(),
          tradingIdeas: await extractTradingIdeas(transcript)
        };
        
        setVoiceJournal(prev => [journalEntry, ...prev]);
        
        // Process as chat message
        await handleUserMessage(transcript);
      }
      
    } catch (error) {
      console.error('Voice processing error:', error);
      toast({
        title: "Processing Error",
        description: "Failed to process voice input",
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleUserMessage = async (message: string) => {
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: message,
      timestamp: new Date()
    };
    
    setChatMessages(prev => [...prev, userMessage]);
    
    // Process with AI assistant
    const response = await processIntelligentResponse(message);
    
    const assistantMessage: ChatMessage = {
      id: (Date.now() + 1).toString(),
      type: 'assistant',
      content: response.content,
      timestamp: new Date(),
      data: response.data
    };
    
    setChatMessages(prev => [...prev, assistantMessage]);
    
    // Speak response if enabled
    if (ElevenLabsService.isConfigured()) {
      await speakMessage(response.content);
    }
  };

  const processIntelligentResponse = async (input: string): Promise<{ content: string; data?: any }> => {
    const lowerInput = input.toLowerCase();
    
    // Voice trading commands
    if (lowerInput.includes('buy') || lowerInput.includes('sell')) {
      return await processTradeCommand(input);
    }
    
    // Portfolio queries
    if (lowerInput.includes('portfolio') || lowerInput.includes('holdings')) {
      return await getPortfolioSummary();
    }
    
    // Market analysis
    if (lowerInput.includes('analysis') || lowerInput.includes('chart')) {
      return await performMarketAnalysis(input);
    }
    
    // Risk assessment
    if (lowerInput.includes('risk') || lowerInput.includes('danger')) {
      return await assessRisk(input);
    }
    
    // News and sentiment
    if (lowerInput.includes('news') || lowerInput.includes('sentiment')) {
      return await getMarketSentiment(input);
    }
    
    // Default intelligent response
    return {
      content: "I understand you're asking about trading. Could you be more specific? I can help with trade execution, portfolio analysis, market insights, risk assessment, or any other trading-related questions."
    };
  };

  const processTradeCommand = async (command: string): Promise<{ content: string; data?: any }> => {
    try {
      // Extract trade details using regex
      const buyMatch = command.match(/buy\s+(\d+)\s+(\w+)(?:\s+at\s+(\d+(?:\.\d+)?))?/i);
      const sellMatch = command.match(/sell\s+(\d+)\s+(\w+)(?:\s+at\s+(\d+(?:\.\d+)?))?/i);
      
      if (buyMatch || sellMatch) {
        const match = buyMatch || sellMatch;
        const action = buyMatch ? 'BUY' : 'SELL';
        const quantity = parseInt(match[1]);
        const symbol = match[2].toUpperCase();
        const price = match[3] ? parseFloat(match[3]) : undefined;
        
        // Perform risk analysis
        const riskAnalysis = await analyzeTradeRisk(symbol, action, quantity, price);
        
        const analysis: TradeAnalysis = {
          symbol,
          action: action as 'BUY' | 'SELL',
          quantity,
          price,
          confidence: riskAnalysis.confidence,
          reasoning: riskAnalysis.reasoning,
          riskLevel: riskAnalysis.riskLevel
        };
        
        setAnalysisResults(prev => [analysis, ...prev]);
        
        return {
          content: `Trade Analysis for ${action} ${quantity} ${symbol}${price ? ` at $${price}` : ''}:\n\nRisk Level: ${riskAnalysis.riskLevel}\nConfidence: ${riskAnalysis.confidence}%\n\nReasoning: ${riskAnalysis.reasoning}\n\nWould you like me to execute this trade?`,
          data: analysis
        };
      }
      
      return {
        content: "I couldn't parse that trade command. Please use format like 'Buy 100 AAPL' or 'Sell 50 TSLA at 250'"
      };
      
    } catch (error) {
      return {
        content: "Error analyzing trade command. Please try again."
      };
    }
  };

  const analyzeTradeRisk = async (symbol: string, action: string, quantity: number, price?: number) => {
    // Simplified risk analysis - in real app, use more sophisticated models
    const positions = await getCurrentPositions();
    const existingPosition = positions.find(p => p.symbol === symbol);
    
    let riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' = 'MEDIUM';
    let confidence = 70;
    let reasoning = `${action} order for ${quantity} shares of ${symbol}.`;
    
    if (existingPosition) {
      if (action === 'SELL' && existingPosition.quantity < quantity) {
        riskLevel = 'HIGH';
        confidence = 40;
        reasoning += ' Warning: Attempting to sell more shares than currently held.';
      } else if (action === 'BUY' && existingPosition.quantity > 1000) {
        riskLevel = 'MEDIUM';
        confidence = 60;
        reasoning += ' Notice: Large existing position, consider diversification.';
      }
    }
    
    if (quantity > 500) {
      riskLevel = 'HIGH';
      confidence = Math.max(30, confidence - 20);
      reasoning += ' High quantity trade detected.';
    }
    
    return { riskLevel, confidence, reasoning };
  };

  const getPortfolioSummary = async (): Promise<{ content: string; data?: any }> => {
    try {
      const positions = await getCurrentPositions();
      const orders = await getCurrentOrders();
      
      const totalValue = positions.reduce((sum, pos) => sum + (pos.quantity * pos.currentPrice), 0);
      const totalGainLoss = positions.reduce((sum, pos) => sum + pos.unrealizedPnL, 0);
      const gainLossPercent = totalValue > 0 ? (totalGainLoss / (totalValue - totalGainLoss)) * 100 : 0;
      
      const content = `Portfolio Summary:
      
Total Value: $${totalValue.toLocaleString()}
Unrealized P&L: $${totalGainLoss.toLocaleString()} (${gainLossPercent.toFixed(2)}%)
Active Positions: ${positions.length}
Pending Orders: ${orders.length}

Top Holdings:
${positions.slice(0, 3).map(pos => 
  `• ${pos.symbol}: ${pos.quantity} shares ($${(pos.quantity * pos.currentPrice).toLocaleString()})`
).join('\n')}`;

      return { content, data: { positions, orders, totalValue, totalGainLoss } };
      
    } catch (error) {
      return { content: "Unable to retrieve portfolio information at this time." };
    }
  };

  const performMarketAnalysis = async (query: string): Promise<{ content: string; data?: any }> => {
    // Extract symbol from query
    const symbolMatch = query.match(/\b([A-Z]{1,5})\b/);
    const symbol = symbolMatch ? symbolMatch[1] : 'MARKET';
    
    return {
      content: `Performing analysis for ${symbol}. Based on current market conditions and technical indicators, here's my assessment:\n\n• Trend: Currently in a consolidation phase\n• Support: Key support levels identified\n• Resistance: Multiple resistance zones ahead\n• Volume: Above average trading volume\n\nRecommendation: Monitor for breakout signals before taking position.`,
      data: { symbol, analysis: 'consolidation' }
    };
  };

  const assessRisk = async (query: string): Promise<{ content: string; data?: any }> => {
    return {
      content: "Current market risk assessment:\n\n🔴 High Risk Factors:\n• Market volatility elevated\n• Economic uncertainty\n\n🟡 Medium Risk Factors:\n• Sector rotation ongoing\n• Earnings season approaching\n\n🟢 Low Risk Factors:\n• Strong economic fundamentals\n• Stable interest rates\n\nRecommendation: Maintain defensive position sizing and consider hedging strategies."
    };
  };

  const getMarketSentiment = async (query: string): Promise<{ content: string; data?: any }> => {
    return {
      content: "Current market sentiment analysis:\n\n📈 Overall Sentiment: Cautiously Optimistic\n\n• Social Media: 62% Bullish\n• News Sentiment: 58% Positive\n• Institutional Flow: Mixed\n• Fear & Greed Index: 45 (Neutral)\n\nKey themes: Earnings expectations, Fed policy, global economic outlook."
    };
  };

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;
    
    try {
      const base64 = await convertToBase64(file);
      setUploadedImage(base64);
      
      // Analyze image with Google Cloud Vision
      const analysis = await GoogleCloudService.analyzeImage(base64.split(',')[1]);
      
      let analysisText = "Chart analysis from uploaded image:\n\n";
      
      if (analysis.textAnnotations?.length > 0) {
        analysisText += "• Text detected: Chart contains price data and indicators\n";
      }
      
      if (analysis.localizedObjectAnnotations?.length > 0) {
        analysisText += "• Objects detected: Technical analysis elements identified\n";
      }
      
      analysisText += "• Pattern Recognition: Analyzing trends and support/resistance levels\n";
      analysisText += "• Recommendation: Based on visual analysis, monitor for key level breaks";
      
      await handleUserMessage(`Please analyze this chart image: ${analysisText}`);
      
    } catch (error) {
      toast({
        title: "Image Analysis Error",
        description: "Failed to analyze uploaded image",
        variant: "destructive"
      });
    }
  };

  const convertToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = error => reject(error);
    });
  };

  const speakMessage = async (text: string) => {
    if (!ElevenLabsService.isConfigured()) return;
    
    try {
      setIsSpeaking(true);
      const audioBuffer = await ElevenLabsService.textToSpeech(text);
      await ElevenLabsService.playAudio(audioBuffer);
    } catch (error) {
      console.error('Speech synthesis error:', error);
    } finally {
      setIsSpeaking(false);
    }
  };

  const summarizeVoiceInput = async (transcript: string): Promise<string> => {
    // Simple summarization - in production, use more sophisticated NLP
    const words = transcript.split(' ');
    return words.length > 20 ? words.slice(0, 20).join(' ') + '...' : transcript;
  };

  const extractTradingIdeas = async (transcript: string): Promise<string[]> => {
    const ideas: string[] = [];
    const symbols = transcript.match(/\b[A-Z]{1,5}\b/g) || [];
    const actions = transcript.match(/\b(buy|sell|hold)\b/gi) || [];
    
    symbols.forEach(symbol => {
      actions.forEach(action => {
        ideas.push(`${action.toUpperCase()} ${symbol}`);
      });
    });
    
    return ideas;
  };

  const toggleVoiceRecording = () => {
    if (isListening) {
      stopVoiceRecording();
    } else {
      startVoiceRecording();
    }
  };

  return (
    <div className="space-y-6">
      <Card className="backdrop-blur-md bg-card/50 border-primary/20">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            Intelligent Trading Assistant
          </CardTitle>
        </CardHeader>
        
        <CardContent>
          <Tabs defaultValue="chat" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="chat">AI Chat</TabsTrigger>
              <TabsTrigger value="voice">Voice Journal</TabsTrigger>
              <TabsTrigger value="analysis">Trade Analysis</TabsTrigger>
              <TabsTrigger value="vision">Chart Vision</TabsTrigger>
            </TabsList>
            
            <TabsContent value="chat" className="space-y-4">
              <ScrollArea className="h-96 w-full border rounded-lg p-4">
                <AnimatePresence>
                  {chatMessages.map((message) => (
                    <motion.div
                      key={message.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      className={`mb-4 ${message.type === 'user' ? 'text-right' : 'text-left'}`}
                    >
                      <div className={`inline-block max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                        message.type === 'user' 
                          ? 'bg-primary text-primary-foreground' 
                          : 'bg-muted'
                      }`}>
                        <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                        <p className="text-xs opacity-70 mt-1">
                          {message.timestamp.toLocaleTimeString()}
                        </p>
                      </div>
                    </motion.div>
                  ))}
                </AnimatePresence>
              </ScrollArea>
              
              <div className="flex gap-2">
                <Input
                  value={currentInput}
                  onChange={(e) => setCurrentInput(e.target.value)}
                  placeholder="Ask me anything about trading..."
                  onKeyPress={(e) => {
                    if (e.key === 'Enter' && currentInput.trim()) {
                      handleUserMessage(currentInput);
                      setCurrentInput('');
                    }
                  }}
                  disabled={isProcessing}
                />
                
                <Button
                  onClick={toggleVoiceRecording}
                  variant={isListening ? "destructive" : "outline"}
                  size="icon"
                  disabled={isProcessing}
                >
                  {isListening ? <MicOff className="h-4 w-4" /> : <Mic className="h-4 w-4" />}
                </Button>
                
                <Button
                  onClick={() => {
                    if (currentInput.trim()) {
                      handleUserMessage(currentInput);
                      setCurrentInput('');
                    }
                  }}
                  disabled={!currentInput.trim() || isProcessing}
                >
                  Send
                </Button>
              </div>
              
              {(isListening || isProcessing || isSpeaking) && (
                <div className="flex gap-2">
                  {isListening && <Badge variant="destructive">Recording</Badge>}
                  {isProcessing && <Badge variant="outline">Processing</Badge>}
                  {isSpeaking && <Badge variant="secondary">Speaking</Badge>}
                </div>
              )}
            </TabsContent>
            
            <TabsContent value="voice" className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">Voice Trading Journal</h3>
                <Button onClick={toggleVoiceRecording} variant="outline" size="sm">
                  <Mic className="h-4 w-4 mr-2" />
                  Add Entry
                </Button>
              </div>
              
              <ScrollArea className="h-80">
                {voiceJournal.map((entry) => (
                  <Card key={entry.id} className="mb-4">
                    <CardContent className="p-4">
                      <div className="flex justify-between items-start mb-2">
                        <Badge variant="outline">{entry.sentiment}</Badge>
                        <span className="text-xs text-muted-foreground">
                          {entry.timestamp.toLocaleString()}
                        </span>
                      </div>
                      <p className="text-sm mb-2">{entry.transcript}</p>
                      {entry.tradingIdeas && entry.tradingIdeas.length > 0 && (
                        <div className="flex gap-1 flex-wrap">
                          {entry.tradingIdeas.map((idea, idx) => (
                            <Badge key={idx} variant="secondary" className="text-xs">
                              {idea}
                            </Badge>
                          ))}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </ScrollArea>
            </TabsContent>
            
            <TabsContent value="analysis" className="space-y-4">
              <h3 className="text-lg font-semibold">Trade Analysis Results</h3>
              
              <ScrollArea className="h-80">
                {analysisResults.map((analysis, index) => (
                  <Card key={index} className="mb-4">
                    <CardContent className="p-4">
                      <div className="flex justify-between items-start mb-2">
                        <div className="flex items-center gap-2">
                          <Badge variant={analysis.action === 'BUY' ? 'default' : 'destructive'}>
                            {analysis.action}
                          </Badge>
                          <span className="font-semibold">{analysis.symbol}</span>
                          <span className="text-sm">Qty: {analysis.quantity}</span>
                        </div>
                        <Badge 
                          variant={
                            analysis.riskLevel === 'LOW' ? 'default' : 
                            analysis.riskLevel === 'MEDIUM' ? 'secondary' : 'destructive'
                          }
                        >
                          {analysis.riskLevel} RISK
                        </Badge>
                      </div>
                      
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <TrendingUp className="h-4 w-4" />
                          <span className="text-sm">Confidence: {analysis.confidence}%</span>
                        </div>
                        {analysis.price && (
                          <div className="flex items-center gap-2">
                            <DollarSign className="h-4 w-4" />
                            <span className="text-sm">Target Price: ${analysis.price}</span>
                          </div>
                        )}
                        <p className="text-sm text-muted-foreground">{analysis.reasoning}</p>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </ScrollArea>
            </TabsContent>
            
            <TabsContent value="vision" className="space-y-4">
              <div className="text-center">
                <h3 className="text-lg font-semibold mb-4">Chart Analysis with AI Vision</h3>
                
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                />
                
                <Button
                  onClick={() => fileInputRef.current?.click()}
                  variant="outline"
                  className="mb-4"
                >
                  <Camera className="h-4 w-4 mr-2" />
                  Upload Chart Screenshot
                </Button>
                
                {uploadedImage && (
                  <div className="mt-4">
                    <img 
                      src={uploadedImage} 
                      alt="Uploaded chart" 
                      className="max-w-full h-auto rounded-lg border"
                    />
                  </div>
                )}
                
                <div className="text-left mt-4 p-4 bg-muted rounded-lg">
                  <h4 className="font-semibold mb-2">Supported Analysis:</h4>
                  <ul className="text-sm space-y-1">
                    <li>• Trend line detection</li>
                    <li>• Support & resistance levels</li>
                    <li>• Chart pattern recognition</li>
                    <li>• Technical indicator analysis</li>
                    <li>• Price target identification</li>
                  </ul>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};
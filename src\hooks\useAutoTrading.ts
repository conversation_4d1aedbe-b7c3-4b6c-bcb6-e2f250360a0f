
import { useState, useEffect, useCallback } from 'react';
import { TradeExecutionEngine, ExecutionConfig } from '@/services/TradeExecutionEngine';
import { TradeExecution } from '@/types/broker';
import { MarketSignal } from '@/types/signals';
import { LiveBroker } from '@/services/LiveBroker';
import { AccountInfo, RiskParameters } from '@/types/trading';

const defaultExecutionConfig: ExecutionConfig = {
  autoExecuteSignals: false,
  minConfidenceThreshold: 75,
  maxSignalsPerHour: 2,
  enableStopLoss: true,
  enableTakeProfit: true,
  defaultRiskPercentage: 1,
  signalTypes: ['TECHNICAL', 'AI_PREDICTION']
};

interface UseAutoTradingProps {
  broker: LiveBroker;
  isConnected: boolean;
  account: AccountInfo | null;
  riskParams: RiskParameters | null;
  signals: MarketSignal[];
}

export const useAutoTrading = ({ broker, isConnected, account, riskParams, signals }: UseAutoTradingProps) => {
  const [executionEngine, setExecutionEngine] = useState<TradeExecutionEngine | null>(null);
  const [executionConfig, setExecutionConfig] = useState<ExecutionConfig>(defaultExecutionConfig);
  const [executions, setExecutions] = useState<TradeExecution[]>([]);
  const [isAutoTrading, setIsAutoTrading] = useState(false);

  // Initialize execution engine
  useEffect(() => {
    if (isConnected && account && riskParams) {
      const engine = new TradeExecutionEngine(broker, account, riskParams, executionConfig);
      setExecutionEngine(engine);
      console.log('Live trade execution engine initialized');
    }
  }, [broker, isConnected, account, riskParams, executionConfig]);

  // Process new signals automatically if auto-trading is enabled
  useEffect(() => {
    if (!executionEngine || !isAutoTrading) return;

    const processNewSignals = async () => {
      const recentSignals = signals.filter(signal => 
        signal.timestamp.getTime() > Date.now() - 60000 && // Last minute
        !executions.some(exec => exec.signalId === signal.id) &&
        signal.confidence >= executionConfig.minConfidenceThreshold
      );

      for (const signal of recentSignals) {
        try {
          console.log(`Processing live signal: ${signal.symbol} ${signal.signal} (${signal.confidence}%)`);
          const execution = await executionEngine.processSignal(signal);
          setExecutions(prev => [execution, ...prev]);
        } catch (error) {
          console.error('Error processing live signal:', error);
        }
      }
    };

    const interval = setInterval(processNewSignals, 10000); // Check every 10 seconds for live trading
    return () => clearInterval(interval);
  }, [executionEngine, signals, executions, isAutoTrading, executionConfig.minConfidenceThreshold]);

  const toggleAutoTrading = useCallback(() => {
    setIsAutoTrading(prev => {
      const newState = !prev;
      console.log(`Live auto-trading ${newState ? 'enabled' : 'disabled'}`);
      if (newState) {
        console.warn('LIVE AUTO-TRADING ENABLED - This will place real orders based on signals!');
      }
      return newState;
    });
  }, []);

  const executeSignalManually = useCallback(async (signal: MarketSignal) => {
    if (!executionEngine) {
      throw new Error('Execution engine not initialized');
    }

    try {
      console.log(`Manual execution of live signal: ${signal.symbol} ${signal.signal}`);
      const execution = await executionEngine.processSignal(signal);
      setExecutions(prev => [execution, ...prev.filter(e => e.signalId !== signal.id)]);
      return execution;
    } catch (error) {
      console.error('Error executing live signal manually:', error);
      throw error;
    }
  }, [executionEngine]);

  const updateExecutionConfig = useCallback((updates: Partial<ExecutionConfig>) => {
    setExecutionConfig(prev => {
      const newConfig = { ...prev, ...updates };
      if (executionEngine) {
        executionEngine.updateConfig(newConfig);
      }
      console.log('Live execution config updated:', newConfig);
      return newConfig;
    });
  }, [executionEngine]);

  const getExecutionStats = useCallback(() => {
    if (!executionEngine) return null;
    return executionEngine.getExecutionStats();
  }, [executionEngine]);

  return {
    executionEngine,
    executionConfig,
    executions: executions.slice(0, 50),
    isAutoTrading,
    toggleAutoTrading,
    executeSignalManually,
    updateExecutionConfig,
    getExecutionStats
  };
};
